# 项目配置
PROJECT_NAME="Video Director Pro"
VERSION="1.0.0"
DEBUG=false

# 文件路径配置
DATA_DIR="./data"
INPUT_DIR="./data/input"
OUTPUT_DIR="./data/output"
TEMP_DIR="./data/temp"
CACHE_DIR="./data/cache"

# API配置
GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_PROJECT_ID="your_google_cloud_project_id"
GEMINI_MODEL="gemini-2.5-pro"

FISH_AUDIO_API_KEY="your_fish_audio_api_key_here"
FISH_AUDIO_MODEL="speech-1.6"

# FFmpeg配置
FFMPEG_PATH="ffmpeg"
FFPROBE_PATH="ffprobe"

# 处理配置
MAX_CONCURRENT_TASKS=3
AUDIO_BITRATE=128
VIDEO_CRF=23

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1.0

# API服务配置
API_HOST="0.0.0.0"
API_PORT=8000
API_WORKERS=1

# 日志配置
LOG_LEVEL="INFO"
LOG_FILE="./data/logs/video_director.log"
ENABLE_JSON_LOGS=false
