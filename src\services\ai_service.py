import asyncio
import aiohttp
import json
import base64
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

from ..models.storyboard import StoryboardCollection, Storyboard, NarrationVersion
from ..models.base import TimeRange
from ..utils.exceptions import AIServiceError, VideoAnalysisError
from ..utils.logger import get_logger
from ..utils.retry import api_retry
from config.settings import settings

logger = get_logger(__name__)

class GeminiService:
    """Gemini AI服务封装"""
    
    def __init__(self):
        self.api_key = settings.GEMINI_API_KEY
        self.project_id = settings.GEMINI_PROJECT_ID
        self.model = settings.GEMINI_MODEL
        # 使用Gemini API的直接端点
        self.base_url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model}:generateContent"
        self.upload_url = "https://generativelanguage.googleapis.com/upload/v1beta/files"

    async def upload_video_file(self, video_path: str) -> str:
        """
        上传视频文件到Gemini API并返回文件URI

        Args:
            video_path: 本地视频文件路径

        Returns:
            上传后的文件URI
        """
        try:
            logger.info(f"开始上传视频文件: {video_path}")

            # 读取视频文件
            video_file = Path(video_path)
            if not video_file.exists():
                raise FileNotFoundError(f"Video file not found: {video_path}")

            # 获取文件信息
            file_size = video_file.stat().st_size
            mime_type = self._get_mime_type(video_path)

            logger.info(f"视频文件信息: 大小={file_size} bytes, 类型={mime_type}")

            # 第一步：初始化上传
            upload_url = f"{self.upload_url}?key={self.api_key}"

            headers = {
                "X-Goog-Upload-Protocol": "resumable",
                "X-Goog-Upload-Command": "start",
                "X-Goog-Upload-Header-Content-Length": str(file_size),
                "X-Goog-Upload-Header-Content-Type": mime_type,
                "Content-Type": "application/json"
            }

            metadata = {
                "file": {
                    "display_name": video_file.name
                }
            }

            async with aiohttp.ClientSession() as session:
                # 初始化上传
                async with session.post(upload_url, headers=headers, json=metadata) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Upload initialization failed: {response.status} - {error_text}")
                        raise AIServiceError(f"Upload initialization failed: {response.status}")

                    upload_session_url = response.headers.get("X-Goog-Upload-URL")
                    if not upload_session_url:
                        raise AIServiceError("No upload session URL returned")

                logger.info("上传会话初始化成功")

                # 第二步：上传文件内容
                upload_headers = {
                    "Content-Length": str(file_size),
                    "X-Goog-Upload-Offset": "0",
                    "X-Goog-Upload-Command": "upload, finalize"
                }

                with open(video_path, 'rb') as f:
                    async with session.post(upload_session_url, headers=upload_headers, data=f) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(f"File upload failed: {response.status} - {error_text}")
                            raise AIServiceError(f"File upload failed: {response.status}")

                        result = await response.json()
                        file_uri = result.get("file", {}).get("uri")

                        if not file_uri:
                            raise AIServiceError("No file URI returned from upload")

                        logger.info(f"视频文件上传成功: {file_uri}")

                        # 等待文件处理完成
                        await self._wait_for_file_active(file_uri)

                        return file_uri

        except Exception as e:
            logger.error(f"Video upload failed: {str(e)}")
            raise AIServiceError(f"Failed to upload video: {str(e)}")

    async def _wait_for_file_active(self, file_uri: str, max_wait_seconds: int = 300):
        """
        等待文件状态变为ACTIVE

        Args:
            file_uri: 文件URI
            max_wait_seconds: 最大等待时间(秒)
        """
        logger.info("等待文件处理完成...")

        # 从URI中提取文件名
        file_name = file_uri.split('/')[-1]
        check_url = f"https://generativelanguage.googleapis.com/v1beta/files/{file_name}?key={self.api_key}"

        import time
        start_time = time.time()

        async with aiohttp.ClientSession() as session:
            while time.time() - start_time < max_wait_seconds:
                async with session.get(check_url) as response:
                    if response.status == 200:
                        result = await response.json()
                        state = result.get("state", "UNKNOWN")

                        logger.info(f"文件状态: {state}")

                        if state == "ACTIVE":
                            logger.info("文件处理完成，状态为ACTIVE")
                            return
                        elif state == "FAILED":
                            raise AIServiceError("File processing failed")

                        # 等待5秒后重试
                        await asyncio.sleep(5)
                    else:
                        logger.warning(f"Failed to check file status: {response.status}")
                        await asyncio.sleep(5)

        raise AIServiceError(f"File did not become ACTIVE within {max_wait_seconds} seconds")

    def _get_mime_type(self, file_path: str) -> str:
        """根据文件扩展名获取MIME类型"""
        extension = Path(file_path).suffix.lower()
        mime_types = {
            '.mp4': 'video/mp4',
            '.avi': 'video/x-msvideo',
            '.mov': 'video/quicktime',
            '.mkv': 'video/x-matroska',
            '.webm': 'video/webm',
            '.flv': 'video/x-flv'
        }
        return mime_types.get(extension, 'video/mp4')

    @api_retry
    async def analyze_video(
        self, 
        video_path: str, 
        prompt: str,
        temperature: float = 1.0,
        timeout: int = 1200
    ) -> Dict[str, Any]:
        """
        分析视频并生成分镜脚本
        
        Args:
            video_path: 视频文件路径
            prompt: AI提示词
            temperature: 创造性参数
            timeout: 超时时间(秒)
            
        Returns:
            AI分析结果
        """
        try:
            logger.info(f"开始分析视频: {video_path}")

            if settings.USE_REAL_API:
                # 调用真实的Gemini API
                logger.info("使用真实Gemini API")

                # 第一步：上传视频文件
                file_uri = await self.upload_video_file(video_path)

                # 第二步：构建包含视频的请求数据
                request_data = {
                    "contents": [{
                        "parts": [
                            {
                                "file_data": {
                                    "mime_type": self._get_mime_type(video_path),
                                    "file_uri": file_uri
                                }
                            },
                            {
                                "text": prompt
                            }
                        ]
                    }],
                    "generationConfig": {
                        "temperature": temperature,
                        "maxOutputTokens": 8192,
                        "responseMimeType": "application/json"
                    }
                }

                # 调用真实的Gemini API
                headers = {
                    "Content-Type": "application/json"
                }

                # 使用API Key作为查询参数
                url_with_key = f"{self.base_url}?key={self.api_key}"

                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                    async with session.post(
                        url_with_key,
                        headers=headers,
                        json=request_data
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(f"Gemini API error: {response.status} - {error_text}")
                            raise AIServiceError(f"Gemini API error: {response.status}")

                        result = await response.json()
                        logger.info("视频分析完成")
                        return result
            else:
                # 使用模拟响应
                logger.info("使用模拟Gemini API响应")
                mock_response = {
                    "candidates": [{
                        "content": {
                            "parts": [{
                                "text": '''```json
{
  "storyboards": [
    {
      "scene_id": 1,
      "source_start_time": "00:00:00.000",
      "source_end_time": "00:00:08.500",
      "duration_seconds": 8.5,
      "narration_script": "大家好，这里是翔宇电影，今天我们来聊聊这部让人又爱又恨的电影。"
    },
    {
      "scene_id": 2,
      "source_start_time": "00:00:08.500",
      "source_end_time": "00:00:16.200",
      "duration_seconds": 7.7,
      "narration_script": "故事的开头就充满了悬念，主角的出场方式简直让人摸不着头脑。"
    },
    {
      "scene_id": 3,
      "source_start_time": "00:00:16.200",
      "source_end_time": "00:00:25.000",
      "duration_seconds": 8.8,
      "narration_script": "但是接下来的剧情发展，却让我们看到了导演的真正用意。"
    }
  ]
}
```'''
                            }]
                        }
                    }]
                }

                logger.info("视频分析完成")
                return mock_response
                    
        except asyncio.TimeoutError:
            raise AIServiceError(f"Video analysis timeout after {timeout} seconds")
        except Exception as e:
            logger.error(f"Video analysis failed: {str(e)}")
            raise VideoAnalysisError(f"Failed to analyze video: {str(e)}")
    
    @api_retry
    async def optimize_narration(
        self,
        video_segment_path: str,
        prompt: str,
        temperature: float = 1.0
    ) -> Dict[str, Any]:
        """
        优化单个分镜的旁白

        Args:
            video_segment_path: 视频片段路径
            prompt: 优化提示词
            temperature: 创造性参数

        Returns:
            优化后的旁白版本
        """
        try:
            logger.info(f"开始优化分镜旁白: {video_segment_path}")

            if settings.USE_REAL_API and Path(video_segment_path).exists():
                # 上传视频片段
                file_uri = await self.upload_video_file(video_segment_path)

                # 构建包含视频的请求数据
                request_data = {
                    "contents": [{
                        "parts": [
                            {
                                "file_data": {
                                    "mime_type": self._get_mime_type(video_segment_path),
                                    "file_uri": file_uri
                                }
                            },
                            {
                                "text": prompt
                            }
                        ]
                    }],
                    "generationConfig": {
                        "temperature": temperature,
                        "maxOutputTokens": 2048,
                        "responseMimeType": "application/json"
                    }
                }
            else:
                # 仅使用文本提示
                request_data = {
                    "contents": [{
                        "parts": [{
                            "text": prompt
                        }]
                    }],
                    "generationConfig": {
                        "temperature": temperature,
                        "maxOutputTokens": 2048,
                        "responseMimeType": "application/json"
                    }
                }

            # 调用真实的Gemini API
            headers = {
                "Content-Type": "application/json"
            }

            # 使用API Key作为查询参数
            url_with_key = f"{self.base_url}?key={self.api_key}"

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                async with session.post(
                    url_with_key,
                    headers=headers,
                    json=request_data
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Gemini API error: {response.status} - {error_text}")
                        raise AIServiceError(f"Gemini API error: {response.status}")

                    result = await response.json()
                    logger.info("旁白优化完成")
                    return result
                    
        except Exception as e:
            logger.error(f"Narration optimization failed: {str(e)}")
            raise AIServiceError(f"Failed to optimize narration: {str(e)}")

class AIAnalysisService:
    """AI分析服务的高级封装"""
    
    def __init__(self):
        self.gemini = GeminiService()
    
    async def analyze_video_with_style(
        self,
        video_path: str,
        style_config,
        scene_count: int,
        language: str,
        custom_script: Optional[str] = None
    ) -> StoryboardCollection:
        """
        使用指定风格分析视频
        
        Args:
            video_path: 视频路径
            style_config: 风格配置
            scene_count: 分镜数量
            language: 语言
            custom_script: 自定义文案
            
        Returns:
            分镜集合
        """
        try:
            # 生成分析提示词
            prompt = style_config.get_analysis_prompt(
                scene_count=scene_count,
                language=language,
                custom_script=custom_script
            )
            
            # 调用AI分析
            result = await self.gemini.analyze_video(video_path, prompt)
            
            # 解析结果
            storyboards = self._parse_analysis_result(result)
            
            # 创建分镜集合
            collection = StoryboardCollection(
                project_id=f"project_{id(self)}",
                total_scenes=len(storyboards),
                storyboards=storyboards,
                original_video_path=video_path
            )
            
            return collection
            
        except Exception as e:
            logger.error(f"Video analysis with style failed: {str(e)}")
            raise VideoAnalysisError(f"Failed to analyze video with style: {str(e)}")
    
    async def optimize_scene_narration(
        self,
        storyboard: Storyboard,
        video_segment_path: str,
        style_config,
        language: str
    ) -> List[NarrationVersion]:
        """
        优化单个分镜的旁白
        
        Args:
            storyboard: 分镜对象
            video_segment_path: 视频片段路径
            style_config: 风格配置
            language: 语言
            
        Returns:
            优化后的旁白版本列表
        """
        try:
            # 生成同步提示词
            prompt = style_config.get_sync_prompt(
                scene_id=storyboard.scene_id,
                duration_seconds=storyboard.source_time_range.duration_seconds,
                language=language,
                original_script=storyboard.narration_script
            )
            
            # 调用AI优化
            result = await self.gemini.optimize_narration(video_segment_path, prompt)
            
            # 解析优化结果
            narration_versions = self._parse_narration_result(
                result, 
                storyboard.source_time_range.duration_seconds,
                style_config.narration_speed_multipliers
            )
            
            return narration_versions
            
        except Exception as e:
            logger.error(f"Narration optimization failed for scene {storyboard.scene_id}: {str(e)}")
            raise AIServiceError(f"Failed to optimize narration: {str(e)}")
    
    def _parse_analysis_result(self, result: Dict[str, Any]) -> List[Storyboard]:
        """解析视频分析结果"""
        try:
            # 提取AI响应文本
            content = result.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "")
            
            # 尝试提取JSON
            import re
            json_match = re.search(r'```json\s*([\s\S]*?)```', content)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content.strip()
            
            # 解析JSON
            data = json.loads(json_str)
            storyboards_data = data.get("storyboards", [])
            
            # 转换为Storyboard对象
            storyboards = []
            for sb_data in storyboards_data:
                time_range = TimeRange(
                    start_time=sb_data["source_start_time"],
                    end_time=sb_data["source_end_time"],
                    duration_seconds=sb_data["duration_seconds"]
                )
                
                storyboard = Storyboard(
                    scene_id=sb_data["scene_id"],
                    source_time_range=time_range,
                    narration_script=sb_data["narration_script"]
                )
                
                storyboards.append(storyboard)
            
            return storyboards
            
        except Exception as e:
            logger.error(f"Failed to parse analysis result: {str(e)}")
            raise VideoAnalysisError(f"Failed to parse AI analysis result: {str(e)}")
    
    def _parse_narration_result(
        self,
        result: Dict[str, Any],
        duration: float,
        speed_multipliers: List[float]
    ) -> List[NarrationVersion]:
        """解析旁白优化结果"""
        try:
            # 提取AI响应文本
            content = result.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "")

            logger.debug(f"Raw narration response: {content}")

            # 如果内容为空，使用默认版本
            if not content.strip():
                logger.warning("Empty response from AI, using default narrations")
                return self._create_default_narration_versions(duration, speed_multipliers)

            # 提取JSON
            import re
            json_match = re.search(r'```json\s*([\s\S]*?)```', content)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个内容
                json_str = content.strip()

            # 解析JSON
            try:
                data = json.loads(json_str)
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON, using default narrations")
                return self._create_default_narration_versions(duration, speed_multipliers)

            # 创建旁白版本
            versions = []
            for i, (version_key, multiplier) in enumerate(zip(["narration_v1", "narration_v2", "narration_v3"], speed_multipliers)):
                text = data.get(version_key, "")
                if text:
                    version = NarrationVersion(
                        version=f"v{i+1}",
                        text=text,
                        word_count=len(text),
                        estimated_duration=len(text) / multiplier
                    )
                    versions.append(version)

            # 如果没有解析到任何版本，使用默认版本
            if not versions:
                logger.warning("No valid narration versions found, using defaults")
                return self._create_default_narration_versions(duration, speed_multipliers)

            return versions

        except Exception as e:
            logger.error(f"Failed to parse narration result: {str(e)}")
            # 返回默认版本而不是抛出异常
            return self._create_default_narration_versions(duration, speed_multipliers)

    def _create_default_narration_versions(self, duration: float, speed_multipliers: List[float]) -> List[NarrationVersion]:
        """创建默认的旁白版本"""
        versions = []
        base_text = "这是一个精彩的片段"

        for i, multiplier in enumerate(speed_multipliers):
            target_length = int(duration * multiplier)
            if target_length <= len(base_text):
                text = base_text[:target_length]
            else:
                text = base_text + "，" * ((target_length - len(base_text)) // 1)
                text = text[:target_length]

            version = NarrationVersion(
                version=f"v{i+1}",
                text=text,
                word_count=len(text),
                estimated_duration=len(text) / multiplier
            )
            versions.append(version)

        return versions
