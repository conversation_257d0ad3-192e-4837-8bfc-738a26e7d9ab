"""自定义异常类"""

class VideoDirectorError(Exception):
    """基础异常类"""
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}

class ValidationError(VideoDirectorError):
    """数据验证异常"""
    pass

class ConfigurationError(VideoDirectorError):
    """配置错误异常"""
    pass

class FileManagementError(VideoDirectorError):
    """文件管理异常"""
    pass

class AIServiceError(VideoDirectorError):
    """AI服务异常"""
    pass

class VideoAnalysisError(AIServiceError):
    """视频分析异常"""
    pass

class TTSServiceError(VideoDirectorError):
    """语音合成服务异常"""
    pass

class AudioProcessingError(TTSServiceError):
    """音频处理异常"""
    pass

class VideoProcessingError(VideoDirectorError):
    """视频处理异常"""
    pass

class FFmpegError(VideoProcessingError):
    """FFmpeg异常"""
    pass

class WorkflowError(VideoDirectorError):
    """工作流异常"""
    pass

class RetryableError(VideoDirectorError):
    """可重试异常"""
    def __init__(self, message: str, retry_after: float = 1.0, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after

class RateLimitError(RetryableError):
    """速率限制异常"""
    def __init__(self, message: str, retry_after: float = 60.0, **kwargs):
        super().__init__(message, retry_after=retry_after, **kwargs)

class NetworkError(RetryableError):
    """网络异常"""
    pass

class ResourceExhaustionError(VideoDirectorError):
    """资源耗尽异常"""
    pass

class TimeoutError(VideoDirectorError):
    """超时异常"""
    pass
