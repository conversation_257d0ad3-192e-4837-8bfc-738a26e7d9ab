from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class ProcessingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"

class BaseEntity(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    status: ProcessingStatus = ProcessingStatus.PENDING
    metadata: Dict[str, Any] = Field(default_factory=dict)

class TimeRange(BaseModel):
    start_time: str = Field(..., description="开始时间 HH:MM:SS.ms")
    end_time: str = Field(..., description="结束时间 HH:MM:SS.ms")
    duration_seconds: float = Field(..., description="时长(秒)")
    
    @property
    def duration_ms(self) -> int:
        return int(self.duration_seconds * 1000)
