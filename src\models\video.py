from pydantic import BaseModel, Field, validator
from typing import Optional, List
from .base import BaseEntity, TimeRange
from .audio import AudioFile

class VideoMetadata(BaseModel):
    width: int = Field(..., description="视频宽度")
    height: int = Field(..., description="视频高度")
    fps: float = Field(..., description="帧率")
    duration: float = Field(..., description="时长(秒)")
    codec: str = Field(default="libx264", description="视频编码")
    bitrate: int = Field(..., description="比特率")
    file_size: int = Field(..., description="文件大小(字节)")

class VideoSegment(BaseEntity):
    """视频片段"""
    scene_id: int = Field(..., description="对应分镜ID")
    source_video_path: str = Field(..., description="原视频路径")
    segment_path: str = Field(..., description="片段文件路径")
    time_range: TimeRange = Field(..., description="时间范围")
    metadata: VideoMetadata = Field(..., description="视频元数据")
    
class ProcessedSegment(BaseEntity):
    """处理后的视频片段"""
    original_segment: VideoSegment
    audio_file: AudioFile
    speed_ratio: float = Field(..., description="调速比例")
    synced_video_path: str = Field(..., description="调速后视频路径")
    final_segment_path: str = Field(..., description="最终合成片段路径")
    
    @validator('speed_ratio')
    def validate_speed_ratio(cls, v):
        if v <= 0:
            raise ValueError('speed_ratio must be positive')
        return v

class VideoProject(BaseEntity):
    """视频项目"""
    name: str = Field(..., description="项目名称")
    input_video_path: str = Field(..., description="输入视频路径")
    output_video_path: Optional[str] = Field(None, description="输出视频路径")
    style: str = Field(..., description="剪辑风格")
    language: str = Field(..., description="语言")
    scene_count: int = Field(..., description="分镜数量")
    custom_script: Optional[str] = Field(None, description="自定义文案")
    
    # 处理状态
    segments: List[VideoSegment] = Field(default_factory=list)
    processed_segments: List[ProcessedSegment] = Field(default_factory=list)
    
    @property
    def is_complete(self) -> bool:
        return (len(self.processed_segments) == self.scene_count and 
                self.output_video_path is not None)
