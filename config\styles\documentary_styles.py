from .base import StyleConfig, StyleRegistry

class NaturalDocumentaryStyle(StyleConfig):
    """俏皮自然纪录片风格"""
    name: str = "俏皮自然纪录片风格"
    description: str = "轻松幽默、贴近自然的纪录片解说风格"
    channel_name: str = "翔宇纪录"
    voice_id: str = "605d4ecb2fda4be9872b5fbfada888a1"
    language_voices: dict = {
        "中文": "605d4ecb2fda4be9872b5fbfada888a1",
        "英语": "c8d5b2f3a4e6789012345678901234ab"
    }
    
    def get_analysis_prompt(self, **kwargs) -> str:
        scene_count = kwargs.get('scene_count', 10)
        language = kwargs.get('language', '中文')
        
        return f"""# AI俏皮自然纪录片风格导演

你是一位专精自然纪录片的AI导演，擅长用轻松幽默的方式讲述自然故事。

## 创作特色
1. **俏皮解说**：用生动有趣的语言描述自然现象
2. **科普价值**：在娱乐中传递知识
3. **情感温度**：展现对自然的敬畏和热爱

## 输入信息
- 分镜数量: {scene_count}
- 频道名称: {self.channel_name}
- 语言: {language}
- 时长范围: {self.min_scene_duration}-{self.max_scene_duration}秒

输出纯净JSON格式的分镜脚本，每个分镜都要体现俏皮自然的风格特色。"""
    
    def get_sync_prompt(self, **kwargs) -> str:
        scene_id = kwargs.get('scene_id')
        duration = kwargs.get('duration_seconds')
        language = kwargs.get('language', '中文')
        original_script = kwargs.get('original_script', '')
        
        return f"""# 俏皮自然纪录片风格旁白优化

为分镜{scene_id}（{duration}秒）优化旁白，保持俏皮自然的纪录片风格。

原始脚本: {original_script}

要求：
1. 保持轻松幽默的语调
2. 融入自然科普元素
3. 体现对自然的热爱

生成三个版本：
- v1: {int(duration * self.narration_speed_multipliers[0])} 字
- v2: {int(duration * self.narration_speed_multipliers[1])} 字
- v3: {int(duration * self.narration_speed_multipliers[2])} 字"""

# 注册风格
StyleRegistry.register(NaturalDocumentaryStyle())
