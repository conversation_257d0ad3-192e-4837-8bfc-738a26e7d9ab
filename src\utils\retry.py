import asyncio
import functools
import logging
from typing import Callable, Type, Union, <PERSON>ple
import random

from .exceptions import RetryableError, RateLimitError, NetworkError
from .logger import get_logger

logger = get_logger(__name__)

def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    jitter: bool = True,
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = (RetryableError,),
    on_retry: Callable = None
):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {str(e)}")
                        raise e
                    
                    # 计算延迟时间
                    if isinstance(e, RetryableError) and hasattr(e, 'retry_after'):
                        wait_time = e.retry_after
                    else:
                        wait_time = current_delay
                        if jitter:
                            wait_time *= (0.5 + random.random())
                    
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{max_attempts}), retrying in {wait_time:.2f}s: {str(e)}")
                    
                    # 调用重试回调
                    if on_retry:
                        try:
                            if asyncio.iscoroutinefunction(on_retry):
                                await on_retry(attempt + 1, e, wait_time)
                            else:
                                on_retry(attempt + 1, e, wait_time)
                        except Exception as callback_error:
                            logger.warning(f"Retry callback failed: {str(callback_error)}")
                    
                    # 等待
                    await asyncio.sleep(wait_time)
                    
                    # 更新延迟时间
                    current_delay *= backoff
                except Exception as e:
                    logger.error(f"Function {func.__name__} failed with non-retryable exception: {str(e)}")
                    raise e
            
            raise last_exception
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {str(e)}")
                        raise e
                    
                    # 计算延迟时间
                    if isinstance(e, RetryableError) and hasattr(e, 'retry_after'):
                        wait_time = e.retry_after
                    else:
                        wait_time = current_delay
                        if jitter:
                            wait_time *= (0.5 + random.random())
                    
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{max_attempts}), retrying in {wait_time:.2f}s: {str(e)}")
                    
                    # 调用重试回调
                    if on_retry:
                        try:
                            on_retry(attempt + 1, e, wait_time)
                        except Exception as callback_error:
                            logger.warning(f"Retry callback failed: {str(callback_error)}")
                    
                    # 等待
                    import time
                    time.sleep(wait_time)
                    
                    # 更新延迟时间
                    current_delay *= backoff
                except Exception as e:
                    logger.error(f"Function {func.__name__} failed with non-retryable exception: {str(e)}")
                    raise e
            
            raise last_exception
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 常用的重试配置
api_retry = retry(
    max_attempts=3,
    delay=1.0,
    backoff=2.0,
    exceptions=(NetworkError, RateLimitError, RetryableError)
)

file_operation_retry = retry(
    max_attempts=2,
    delay=0.5,
    backoff=1.5,
    exceptions=(OSError, IOError)
)

ffmpeg_retry = retry(
    max_attempts=2,
    delay=2.0,
    backoff=1.0,
    exceptions=(Exception,)  # FFmpeg可能抛出各种异常
)
