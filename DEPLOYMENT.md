# Video Director Pro - 部署指南

## 系统要求

### 硬件要求
- **CPU**: 4核心以上推荐
- **内存**: 8GB RAM 最低，16GB 推荐
- **存储**: 至少50GB可用空间
- **网络**: 稳定的互联网连接（用于API调用）

### 软件要求
- **Python**: 3.8+ (推荐3.10+)
- **FFmpeg**: 4.0+ (必需)
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

## 安装步骤

### 1. 安装Python依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 安装FFmpeg

#### Windows
```bash
# 使用Chocolatey
choco install ffmpeg

# 或下载预编译版本
# 从 https://ffmpeg.org/download.html 下载
# 解压并添加到PATH环境变量
```

#### macOS
```bash
# 使用Homebrew
brew install ffmpeg
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

### 3. 配置环境变量

创建 `.env` 文件：

```bash
# 项目配置
PROJECT_NAME="Video Director Pro"
VERSION="1.0.0"
DEBUG=false

# API配置
GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_PROJECT_ID="your_google_cloud_project_id"
GEMINI_MODEL="gemini-2.5-pro"

FISH_AUDIO_API_KEY="your_fish_audio_api_key_here"
FISH_AUDIO_MODEL="speech-1.6"

# 文件路径配置
DATA_DIR="./data"
INPUT_DIR="./data/input"
OUTPUT_DIR="./data/output"
TEMP_DIR="./data/temp"
CACHE_DIR="./data/cache"

# FFmpeg配置
FFMPEG_PATH="ffmpeg"
FFPROBE_PATH="ffprobe"

# 处理配置
MAX_CONCURRENT_TASKS=3
AUDIO_BITRATE=128
VIDEO_CRF=23

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1.0
```

### 4. 获取API密钥

#### Google Gemini API
1. 访问 [Google AI Studio](https://makersuite.google.com/)
2. 创建新项目或选择现有项目
3. 启用Gemini API
4. 创建API密钥
5. 将密钥添加到 `.env` 文件

#### Fish Audio API
1. 访问 [Fish Audio](https://fish.audio/)
2. 注册账户并登录
3. 获取API密钥
4. 将密钥添加到 `.env` 文件

## 验证安装

```bash
# 检查系统状态
python cli/main.py status

# 运行基础测试
python cli/main.py test

# 测试所有组件
python cli/main.py test-ai
python cli/main.py test-tts
python cli/main.py test-video
python cli/main.py test-files
```

## 生产部署

### 1. 性能优化

```bash
# 调整并发参数（在.env中）
MAX_CONCURRENT_TASKS=6  # 根据CPU核心数调整
AUDIO_BITRATE=192       # 提高音频质量
VIDEO_CRF=20           # 提高视频质量
```

### 2. 监控和日志

```bash
# 启用详细日志
DEBUG=false
LOG_LEVEL="INFO"
LOG_FILE="./data/logs/video_director.log"
ENABLE_JSON_LOGS=true
```

### 3. 资源管理

- 定期清理临时文件
- 监控磁盘空间使用
- 设置合适的并发限制

### 4. 安全考虑

- 保护API密钥安全
- 限制文件访问权限
- 定期更新依赖包

## 故障排除

### 常见问题

#### FFmpeg未找到
```bash
# 检查FFmpeg安装
ffmpeg -version

# 如果未安装，按照上述步骤安装
```

#### API密钥错误
- 检查 `.env` 文件中的API密钥是否正确
- 确认API服务可用性
- 检查网络连接

#### 内存不足
- 减少 `MAX_CONCURRENT_TASKS` 值
- 增加系统内存
- 优化视频处理参数

#### 磁盘空间不足
- 清理临时文件：`python -c "from src.services.file_service import CleanupService; import asyncio; asyncio.run(CleanupService().cleanup_temp_files())"`
- 调整缓存策略
- 增加存储空间

### 日志分析

```bash
# 查看最新日志
tail -f ./data/logs/video_director.log

# 搜索错误
grep "ERROR" ./data/logs/video_director.log

# 分析性能
grep "INFO.*完成" ./data/logs/video_director.log
```

## 扩展开发

### 添加新的剪辑风格

1. 在 `config/styles/` 目录下创建新的风格文件
2. 继承 `StyleConfig` 基类
3. 实现必需的方法
4. 注册到 `StyleRegistry`

### 集成新的AI服务

1. 在 `src/services/` 目录下创建新的服务文件
2. 实现异步接口
3. 添加错误处理和重试机制
4. 更新工作流引擎

### 性能监控

- 使用 `asyncio` 性能分析工具
- 监控内存使用情况
- 分析处理时间瓶颈

## 支持

如有问题，请：
1. 检查日志文件
2. 查看故障排除部分
3. 提交Issue到项目仓库
