from pydantic import BaseModel, Field, validator
from typing import Optional, List
from .base import BaseEntity

class AudioMetadata(BaseModel):
    duration: float = Field(..., description="音频时长(秒)")
    sample_rate: int = Field(default=44100, description="采样率")
    channels: int = Field(default=1, description="声道数")
    bitrate: int = Field(default=128, description="比特率")
    format: str = Field(default="mp3", description="音频格式")
    file_size: int = Field(..., description="文件大小(字节)")

class AudioFile(BaseEntity):
    file_path: str = Field(..., description="音频文件路径")
    narration_text: str = Field(..., description="对应的旁白文本")
    voice_id: str = Field(..., description="音色ID")
    language: str = Field(..., description="语言")
    metadata: AudioMetadata = Field(..., description="音频元数据")
    
    @property
    def duration_ms(self) -> int:
        return int(self.metadata.duration * 1000)

class AudioCandidate(BaseModel):
    """音频候选项，用于筛选最佳配音"""
    audio_file: AudioFile
    target_duration: float = Field(..., description="目标时长")
    duration_diff: float = Field(..., description="时长差异")
    match_score: float = Field(..., description="匹配分数 0-1")
    
    @validator('match_score')
    def validate_score(cls, v):
        if not 0 <= v <= 1:
            raise ValueError('match_score must be between 0 and 1')
        return v

class AudioSelection(BaseModel):
    """音频选择结果"""
    scene_id: int
    candidates: List[AudioCandidate]
    selected: AudioCandidate
    selection_reason: str = Field(..., description="选择原因")
