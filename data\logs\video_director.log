2025-07-21 14:54:28,980 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 14:54:28,980 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 14:54:28,980 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 14:54:28,980 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 14:54:28,981 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 14:54:28,981 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 14:54:28,991 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 14:54:28,991 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 14:54:28,991 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 14:54:28,993 - src.workflow.engine - INFO - process_video:74 - 开始处理视频项目: Test_.\data\input\xierdatest.mp4
2025-07-21 14:54:28,993 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 14:54:28,996 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 14:54:28,996 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 14:54:28,996 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project 853d9d99-bfd2-478d-a26c-3d8ccc3996ca
2025-07-21 14:54:28,997 - src.workflow.engine - INFO - process_video:98 - 阶段1: 开始视频分析
2025-07-21 14:54:28,998 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: data\input\data\input\xierdatest.mp4
2025-07-21 14:54:28,998 - src.services.ai_service - INFO - analyze_video:87 - 视频分析完成
2025-07-21 14:54:28,999 - src.workflow.engine - INFO - process_video:114 - 阶段2: 开始视频拆条
2025-07-21 14:54:28,999 - src.services.video_service - INFO - split_video:90 - 开始拆分视频: data\input\data\input\xierdatest.mp4 -> 3 个片段
2025-07-21 14:54:28,999 - src.services.video_service - INFO - get_video_metadata:49 - 获取视频元数据: data\input\data\input\xierdatest.mp4
2025-07-21 14:54:29,000 - src.services.video_service - INFO - get_video_metadata:62 - 视频元数据获取成功: 1920x1080, 30.0s
2025-07-21 14:54:29,000 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 1: 00:00:00.000 -> 8.5s
2025-07-21 14:54:29,000 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_001.mp4
2025-07-21 14:54:29,001 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 2: 00:00:08.500 -> 7.7s
2025-07-21 14:54:29,001 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_002.mp4
2025-07-21 14:54:29,001 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 3: 00:00:16.200 -> 8.8s
2025-07-21 14:54:29,002 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_003.mp4
2025-07-21 14:54:29,002 - src.services.video_service - INFO - split_video:132 - 视频拆分完成: 3 个片段
2025-07-21 14:54:29,003 - src.workflow.engine - INFO - process_video:130 - 阶段3: 开始旁白优化和语音合成
2025-07-21 14:54:29,003 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_001.mp4
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_002.mp4
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_003.mp4
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 14:54:29,004 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,005 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,005 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,006 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,006 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,006 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,007 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,007 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,007 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,010 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,010 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,010 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,011 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,011 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,011 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - select_best_audio:253 - Scene 1: Selected audio with duration 1.00s (target: 8.50s, diff: 7.50s, score: 0.000)
2025-07-21 14:54:29,013 - src.services.tts_service - INFO - select_best_audio:253 - Scene 2: Selected audio with duration 1.00s (target: 7.70s, diff: 6.70s, score: 0.000)
2025-07-21 14:54:29,013 - src.services.tts_service - INFO - select_best_audio:253 - Scene 3: Selected audio with duration 1.00s (target: 8.80s, diff: 7.80s, score: 0.000)
2025-07-21 14:54:29,013 - src.workflow.engine - INFO - process_video:143 - 阶段4: 开始视频处理和音画同步
2025-07-21 14:54:29,014 - src.services.video_service - INFO - process_all_segments:438 - 开始处理 3 个视频片段
2025-07-21 14:54:29,014 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 8.500
2025-07-21 14:54:29,014 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_001.mp4 -> 8.5x
2025-07-21 14:54:29,015 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_1.mp4
2025-07-21 14:54:29,015 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_1.mp4 + data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_1.mp4
2025-07-21 14:54:29,016 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_1.mp4
2025-07-21 14:54:29,016 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_1.mp4
2025-07-21 14:54:29,017 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 2: 速度比例 7.700
2025-07-21 14:54:29,017 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_002.mp4 -> 7.7x
2025-07-21 14:54:29,018 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_2.mp4
2025-07-21 14:54:29,018 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_2.mp4 + data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_2.mp4
2025-07-21 14:54:29,020 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_2.mp4
2025-07-21 14:54:29,020 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_2.mp4
2025-07-21 14:54:29,020 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 3: 速度比例 8.800
2025-07-21 14:54:29,020 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_003.mp4 -> 8.8x
2025-07-21 14:54:29,021 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_3.mp4
2025-07-21 14:54:29,021 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_3.mp4 + data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_3.mp4
2025-07-21 14:54:29,027 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_3.mp4
2025-07-21 14:54:29,027 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_3.mp4
2025-07-21 14:54:29,027 - src.services.video_service - INFO - process_all_segments:475 - 所有视频片段处理完成: 3 个
2025-07-21 14:54:29,029 - src.workflow.engine - INFO - process_video:159 - 阶段5: 开始最终视频合成
2025-07-21 14:54:29,029 - src.services.video_service - INFO - create_final_video:501 - 创建最终视频: 3 个片段 -> data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,029 - src.services.video_service - INFO - concatenate_videos:310 - 拼接视频: 3 个文件 -> data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,031 - src.services.video_service - INFO - concatenate_videos:327 - 视频拼接完成: data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,031 - src.services.video_service - INFO - create_final_video:509 - 最终视频创建完成: data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,033 - src.workflow.engine - INFO - process_video:173 - 视频项目处理完成: data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 15:00:36,024 - src.services.ai_service - INFO - analyze_video:47 - 开始分析视频: test_video.mp4
2025-07-21 15:00:36,024 - src.services.ai_service - INFO - analyze_video:51 - 使用真实Gemini API
2025-07-21 15:00:36,246 - src.services.ai_service - ERROR - analyze_video:81 - Gemini API error: 401 - {
  "error": {
    "code": 401,
    "message": "Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.",
    "status": "UNAUTHENTICATED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.ErrorInfo",
        "reason": "ACCESS_TOKEN_TYPE_UNSUPPORTED",
        "metadata": {
          "method": "google.cloud.aiplatform.v1.PredictionService.GenerateContent",
          "service": "aiplatform.googleapis.com"
        }
      }
    ]
  }
}

2025-07-21 15:00:36,247 - src.services.ai_service - ERROR - analyze_video:132 - Video analysis failed: Gemini API error: 401
2025-07-21 15:00:36,247 - src.utils.retry - ERROR - async_wrapper:63 - Function analyze_video failed with non-retryable exception: Failed to analyze video: Gemini API error: 401
2025-07-21 15:00:36,248 - src.services.ai_service - ERROR - analyze_video_with_style:247 - Video analysis with style failed: Failed to analyze video: Gemini API error: 401
2025-07-21 15:01:25,142 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: test_video.mp4
2025-07-21 15:01:25,142 - src.services.ai_service - INFO - analyze_video:52 - 使用真实Gemini API
2025-07-21 15:02:02,790 - src.services.ai_service - INFO - analyze_video:88 - 视频分析完成
2025-07-21 15:02:02,792 - src.services.ai_service - INFO - optimize_narration:157 - 开始优化分镜旁白: test_segment.mp4
2025-07-21 15:02:26,181 - src.services.ai_service - INFO - optimize_narration:193 - 旁白优化完成
2025-07-21 15:02:26,182 - src.services.ai_service - ERROR - _parse_narration_result:378 - Failed to parse narration result: Expecting value: line 1 column 1 (char 0)
2025-07-21 15:02:26,182 - src.services.ai_service - ERROR - optimize_scene_narration:296 - Narration optimization failed for scene 1: Failed to parse narration optimization result: Expecting value: line 1 column 1 (char 0)
2025-07-21 15:03:05,469 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: test_video.mp4
2025-07-21 15:03:05,469 - src.services.ai_service - INFO - analyze_video:52 - 使用真实Gemini API
2025-07-21 15:03:39,345 - src.services.ai_service - INFO - analyze_video:88 - 视频分析完成
2025-07-21 15:03:39,349 - src.services.ai_service - INFO - optimize_narration:157 - 开始优化分镜旁白: test_segment.mp4
2025-07-21 15:04:02,078 - src.services.ai_service - INFO - optimize_narration:193 - 旁白优化完成
2025-07-21 15:04:02,079 - src.services.ai_service - DEBUG - _parse_narration_result:351 - Raw narration response: {
  "narration_v1": "今天这狠人有点东西，开局直接上演百万烟花秀。这波操作，是致敬艺术，还是单纯钱多烧得慌？",
  "narration_v2": "今天这狠人可太秀了，开局就给自己整了场百万烟花秀。你说这到底是行为艺术，还是纯纯的智商税？",
  "narration_v3": "今天这狠人属实玩得有点大，开场白就是一场百万烟花秀。他这波惊为天人的操作，究竟是致敬艺术，还是单纯的钱多、任性、想上天？"
}
2025-07-21 15:04:38,871 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 15:04:38,871 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 15:04:38,872 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 15:04:38,872 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 15:04:38,872 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 15:04:38,872 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 15:04:38,873 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 15:04:38,873 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 15:04:38,873 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 15:04:38,875 - src.workflow.engine - INFO - process_video:74 - 开始处理视频项目: 真实API测试
2025-07-21 15:04:38,876 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 15:04:38,876 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 15:04:38,876 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 15:04:38,876 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 15:04:38,876 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 15:04:38,877 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 15:04:38,877 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 15:04:38,877 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 15:04:38,878 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project dc96d356-4f2c-4eff-bb94-28d38e56d591
2025-07-21 15:04:38,878 - src.workflow.engine - INFO - process_video:98 - 阶段1: 开始视频分析
2025-07-21 15:04:38,878 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: data\input\data\input\xierdatest.mp4
2025-07-21 15:04:38,878 - src.services.ai_service - INFO - analyze_video:52 - 使用真实Gemini API
2025-07-21 15:05:19,119 - src.services.ai_service - INFO - analyze_video:88 - 视频分析完成
2025-07-21 15:05:19,121 - src.workflow.engine - INFO - process_video:114 - 阶段2: 开始视频拆条
2025-07-21 15:05:19,122 - src.services.video_service - INFO - split_video:90 - 开始拆分视频: data\input\data\input\xierdatest.mp4 -> 3 个片段
2025-07-21 15:05:19,122 - src.services.video_service - INFO - get_video_metadata:49 - 获取视频元数据: data\input\data\input\xierdatest.mp4
2025-07-21 15:05:19,122 - src.services.video_service - INFO - get_video_metadata:62 - 视频元数据获取成功: 1920x1080, 30.0s
2025-07-21 15:05:19,122 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 1: 00:01:15.300 -> 9.5s
2025-07-21 15:05:19,122 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_001.mp4
2025-07-21 15:05:19,123 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 2: 00:00:22.150 -> 11.0s
2025-07-21 15:05:19,123 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_002.mp4
2025-07-21 15:05:19,124 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 3: 00:00:58.500 -> 10.5s
2025-07-21 15:05:19,124 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_003.mp4
2025-07-21 15:05:19,124 - src.services.video_service - INFO - split_video:132 - 视频拆分完成: 3 个片段
2025-07-21 15:05:19,125 - src.workflow.engine - INFO - process_video:130 - 阶段3: 开始旁白优化和语音合成
2025-07-21 15:05:19,125 - src.services.ai_service - INFO - optimize_narration:157 - 开始优化分镜旁白: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_001.mp4
2025-07-21 15:05:39,319 - src.services.ai_service - INFO - optimize_narration:193 - 旁白优化完成
2025-07-21 15:05:39,320 - src.services.ai_service - DEBUG - _parse_narration_result:351 - Raw narration response: {
  "narration_v1": "今天来看个狠人，号称能用代码预测未来，结果把自己玩破产。瞅他这小脸煞白、怀疑人生的样，他究竟看到了啥？",
  "narration_v2": "都说科技改变命运，这哥们直接用代码算命，结果三分钟喜提破产。瞅他这小脸煞白、怀疑人生的样，究竟看到了什么骚操作？",
  "narration_v3": "都说知识改变命运，这位哥们儿却想用代码直接算命，结果三分钟就把自己玩到倾家荡产。你瞅瞅他这小脸煞白、瞳孔地震的德行，他究竟在未来画面里，看到了什么骚操作？"
}
2025-07-21 15:05:39,320 - src.services.ai_service - INFO - optimize_narration:157 - 开始优化分镜旁白: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_002.mp4
2025-07-21 15:06:03,677 - src.services.ai_service - INFO - optimize_narration:193 - 旁白优化完成
2025-07-21 15:06:03,678 - src.services.ai_service - DEBUG - _parse_narration_result:351 - Raw narration response: {
  "narration_v1": "时间倒回三天前。主角阿坤，人称“自信坤”，用代码搞出的AI预测股市堪比开挂。师傅苦劝，他却一脸“老登你不懂”的傲慢。",
  "narration_v2": "时间倒回三天前。主角阿坤，人称“自信坤”，拿
2025-07-21 15:06:03,678 - src.services.ai_service - WARNING - _parse_narration_result:371 - Failed to parse JSON, using default narrations
2025-07-21 15:06:03,678 - src.services.ai_service - INFO - optimize_narration:157 - 开始优化分镜旁白: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_003.mp4
2025-07-21 15:06:27,967 - src.services.ai_service - INFO - optimize_narration:193 - 旁白优化完成
2025-07-21 15:06:27,968 - src.services.ai_service - DEBUG - _parse_narration_result:351 - Raw narration response: {
  "narration_v1": "人一飘，智商就掉线。AI的血红警告响彻天际，可自信坤早已被贪婪蒙了心，反手就是一个梭哈，亲手把通往未来的大门焊死。",
  "narration_v2": "人一狂，智商就得凉。AI屏幕疯狂弹出系统异常，可我们的自信坤早已被贪婪冲昏头脑，反手梭哈，把科技给的未来钥匙，硬生生掰断在锁芯里。",
  "narration_v3": "老话说得好，人一飘，智商就拉胯。AI屏幕上系统异常的警告都快闪瞎眼了，可这位“自信坤”早已被贪婪冲昏头脑，反手就是一个梭哈。科技给了他窥探未来的剧本，他却因为傲慢，亲手把生路撕得粉碎。"
}
2025-07-21 15:06:27,968 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 今天来看个狠人，号称能用代码预测未来，结... -> data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,968 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,969 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 都说科技改变命运，这哥们直接用代码算命，... -> data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,969 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,969 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 都说知识改变命运，这位哥们儿却想用代码直... -> data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,970 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,970 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 这是一个精彩的片段，，，，，，，，，，，... -> data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,970 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,971 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 这是一个精彩的片段，，，，，，，，，，，... -> data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,971 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,971 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 这是一个精彩的片段，，，，，，，，，，，... -> data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,971 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,972 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 人一飘，智商就掉线。AI的血红警告响彻天... -> data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,972 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,972 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 人一狂，智商就得凉。AI屏幕疯狂弹出系统... -> data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,972 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:27,973 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 老话说得好，人一飘，智商就拉胯。AI屏幕... -> data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:27,973 - src.services.tts_service - INFO - synthesize_speech:62 - 使用真实Fish Audio API
2025-07-21 15:06:30,337 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:31,304 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:31,762 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:32,673 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:32,812 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:32,813 - src.services.tts_service - INFO - select_best_audio:287 - Scene 1: Selected audio with duration 122.46s (target: 9.50s, diff: 112.96s, score: 0.000)
2025-07-21 15:06:32,978 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:33,172 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:33,173 - src.services.tts_service - INFO - select_best_audio:287 - Scene 3: Selected audio with duration 126.22s (target: 10.50s, diff: 115.72s, score: 0.000)
2025-07-21 15:06:33,231 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:40,468 - src.services.tts_service - INFO - synthesize_speech:113 - 语音合成完成: data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 15:06:40,468 - src.services.tts_service - INFO - select_best_audio:287 - Scene 2: Selected audio with duration 51.83s (target: 11.00s, diff: 40.83s, score: 0.000)
2025-07-21 15:06:40,469 - src.workflow.engine - INFO - process_video:143 - 阶段4: 开始视频处理和音画同步
2025-07-21 15:06:40,470 - src.services.video_service - INFO - process_all_segments:438 - 开始处理 3 个视频片段
2025-07-21 15:06:40,470 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 0.078
2025-07-21 15:06:40,470 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_001.mp4 -> 0.07757508451601312x
2025-07-21 15:06:40,471 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\speed_adjusted_1.mp4
2025-07-21 15:06:40,471 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\speed_adjusted_1.mp4 + data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_1.mp4
2025-07-21 15:06:40,472 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_1.mp4
2025-07-21 15:06:40,473 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_1.mp4
2025-07-21 15:06:40,473 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 2: 速度比例 0.212
2025-07-21 15:06:40,474 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_002.mp4 -> 0.2122486782695944x
2025-07-21 15:06:40,474 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\speed_adjusted_2.mp4
2025-07-21 15:06:40,475 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\speed_adjusted_2.mp4 + data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_2.mp4
2025-07-21 15:06:40,476 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_2.mp4
2025-07-21 15:06:40,476 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_2.mp4
2025-07-21 15:06:40,476 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 3: 速度比例 0.083
2025-07-21 15:06:40,476 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\segments\segment_003.mp4 -> 0.08318610712786101x
2025-07-21 15:06:40,477 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\speed_adjusted_3.mp4
2025-07-21 15:06:40,477 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\speed_adjusted_3.mp4 + data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_3.mp4
2025-07-21 15:06:40,488 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_3.mp4
2025-07-21 15:06:40,488 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\dc96d356-4f2c-4eff-bb94-28d38e56d591\processing\final_segment_3.mp4
2025-07-21 15:06:40,488 - src.services.video_service - INFO - process_all_segments:475 - 所有视频片段处理完成: 3 个
2025-07-21 15:06:40,489 - src.workflow.engine - INFO - process_video:159 - 阶段5: 开始最终视频合成
2025-07-21 15:06:40,489 - src.services.video_service - INFO - create_final_video:501 - 创建最终视频: 3 个片段 -> data\output\dc96d356-4f2c-4eff-bb94-28d38e56d591\真实API测试.mp4
2025-07-21 15:06:40,490 - src.services.video_service - INFO - concatenate_videos:310 - 拼接视频: 3 个文件 -> data\output\dc96d356-4f2c-4eff-bb94-28d38e56d591\真实API测试.mp4
2025-07-21 15:06:40,573 - src.services.video_service - INFO - concatenate_videos:327 - 视频拼接完成: data\output\dc96d356-4f2c-4eff-bb94-28d38e56d591\真实API测试.mp4
2025-07-21 15:06:40,573 - src.services.video_service - INFO - create_final_video:509 - 最终视频创建完成: data\output\dc96d356-4f2c-4eff-bb94-28d38e56d591\真实API测试.mp4
2025-07-21 15:06:40,573 - src.workflow.engine - INFO - process_video:173 - 视频项目处理完成: data\output\dc96d356-4f2c-4eff-bb94-28d38e56d591\真实API测试.mp4
2025-07-21 15:13:53,687 - src.services.ai_service - INFO - analyze_video:145 - 开始分析视频: test_video.mp4
2025-07-21 15:13:53,687 - src.services.ai_service - INFO - analyze_video:149 - 使用真实Gemini API
2025-07-21 15:13:53,687 - src.services.ai_service - INFO - upload_video_file:40 - 开始上传视频文件: test_video.mp4
2025-07-21 15:13:53,687 - src.services.ai_service - ERROR - upload_video_file:108 - Video upload failed: Video file not found: test_video.mp4
2025-07-21 15:13:53,687 - src.services.ai_service - ERROR - analyze_video:243 - Video analysis failed: Failed to upload video: Video file not found: test_video.mp4
2025-07-21 15:13:53,687 - src.utils.retry - ERROR - async_wrapper:63 - Function analyze_video failed with non-retryable exception: Failed to analyze video: Failed to upload video: Video file not found: test_video.mp4
2025-07-21 15:13:53,687 - src.services.ai_service - ERROR - analyze_video_with_style:360 - Video analysis with style failed: Failed to analyze video: Failed to upload video: Video file not found: test_video.mp4
2025-07-21 15:15:46,784 - src.services.ai_service - INFO - analyze_video:145 - 开始分析视频: .\data\input\xierdatest.mp4
2025-07-21 15:15:46,784 - src.services.ai_service - INFO - analyze_video:149 - 使用真实Gemini API
2025-07-21 15:15:46,784 - src.services.ai_service - INFO - upload_video_file:40 - 开始上传视频文件: .\data\input\xierdatest.mp4
2025-07-21 15:15:46,784 - src.services.ai_service - INFO - upload_video_file:51 - 视频文件信息: 大小=54260763 bytes, 类型=video/mp4
2025-07-21 15:15:48,335 - src.services.ai_service - INFO - upload_video_file:82 - 上传会话初始化成功
2025-07-21 15:17:29,234 - src.services.ai_service - INFO - upload_video_file:104 - 视频文件上传成功: https://generativelanguage.googleapis.com/v1beta/files/fqn1zgy5euw9
2025-07-21 15:17:29,726 - src.services.ai_service - ERROR - analyze_video:192 - Gemini API error: 400 - {
  "error": {
    "code": 400,
    "message": "The File fqn1zgy5euw9 is not in an ACTIVE state and usage is not allowed.",
    "status": "FAILED_PRECONDITION"
  }
}

2025-07-21 15:17:29,727 - src.services.ai_service - ERROR - analyze_video:243 - Video analysis failed: Gemini API error: 400
2025-07-21 15:17:29,727 - src.utils.retry - ERROR - async_wrapper:63 - Function analyze_video failed with non-retryable exception: Failed to analyze video: Gemini API error: 400
2025-07-21 15:17:29,727 - src.services.ai_service - ERROR - analyze_video_with_style:360 - Video analysis with style failed: Failed to analyze video: Gemini API error: 400
2025-07-21 15:18:51,477 - src.services.ai_service - INFO - analyze_video:189 - 开始分析视频: .\data\input\xierdatest.mp4
2025-07-21 15:18:51,477 - src.services.ai_service - INFO - analyze_video:193 - 使用真实Gemini API
2025-07-21 15:18:51,477 - src.services.ai_service - INFO - upload_video_file:40 - 开始上传视频文件: .\data\input\xierdatest.mp4
2025-07-21 15:18:51,477 - src.services.ai_service - INFO - upload_video_file:51 - 视频文件信息: 大小=54260763 bytes, 类型=video/mp4
2025-07-21 15:18:52,521 - src.services.ai_service - INFO - upload_video_file:82 - 上传会话初始化成功
2025-07-21 15:20:36,072 - src.services.ai_service - INFO - upload_video_file:104 - 视频文件上传成功: https://generativelanguage.googleapis.com/v1beta/files/amqmt5kkirdc
2025-07-21 15:20:36,072 - src.services.ai_service - INFO - _wait_for_file_active:123 - 等待文件处理完成...
2025-07-21 15:20:37,527 - src.services.ai_service - INFO - _wait_for_file_active:139 - 文件状态: PROCESSING
2025-07-21 15:20:42,861 - src.services.ai_service - INFO - _wait_for_file_active:139 - 文件状态: PROCESSING
2025-07-21 15:20:48,194 - src.services.ai_service - INFO - _wait_for_file_active:139 - 文件状态: PROCESSING
2025-07-21 15:20:53,531 - src.services.ai_service - INFO - _wait_for_file_active:139 - 文件状态: ACTIVE
2025-07-21 15:20:53,532 - src.services.ai_service - INFO - _wait_for_file_active:142 - 文件处理完成，状态为ACTIVE
2025-07-21 15:21:30,355 - src.services.ai_service - INFO - analyze_video:240 - 视频分析完成
2025-07-21 15:21:30,358 - src.services.ai_service - INFO - optimize_narration:309 - 开始优化分镜旁白: test_segment.mp4
2025-07-21 15:21:55,135 - src.services.ai_service - ERROR - optimize_narration:341 - Gemini API error: 500 - {
  "error": {
    "code": 500,
    "message": "An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting",
    "status": "INTERNAL"
  }
}

2025-07-21 15:21:55,136 - src.services.ai_service - ERROR - optimize_narration:349 - Narration optimization failed: Gemini API error: 500
2025-07-21 15:21:55,136 - src.utils.retry - ERROR - async_wrapper:63 - Function optimize_narration failed with non-retryable exception: Failed to optimize narration: Gemini API error: 500
2025-07-21 15:21:55,136 - src.services.ai_service - ERROR - optimize_scene_narration:448 - Narration optimization failed for scene 1: Failed to optimize narration: Gemini API error: 500
2025-07-21 15:23:30,237 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 15:23:30,238 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 15:23:30,238 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 15:23:30,238 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 15:23:30,238 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 15:23:30,238 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 15:23:30,240 - src.workflow.engine - INFO - process_video:74 - 开始处理视频项目: 真实视频分析测试
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 15:23:30,240 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 15:23:30,244 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 15:23:30,245 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 15:23:30,245 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 15:23:30,245 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 15:23:30,256 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project 32825277-a315-4138-a9d9-90fbad35a2cd
2025-07-21 15:23:30,257 - src.workflow.engine - INFO - process_video:98 - 阶段1: 开始视频分析
2025-07-21 15:23:30,257 - src.services.ai_service - INFO - analyze_video:189 - 开始分析视频: data\input\data\input\xierdatest.mp4
2025-07-21 15:23:30,257 - src.services.ai_service - INFO - analyze_video:193 - 使用真实Gemini API
2025-07-21 15:23:30,257 - src.services.ai_service - INFO - upload_video_file:40 - 开始上传视频文件: data\input\data\input\xierdatest.mp4
2025-07-21 15:23:30,257 - src.services.ai_service - INFO - upload_video_file:51 - 视频文件信息: 大小=33000 bytes, 类型=video/mp4
2025-07-21 15:23:31,106 - src.services.ai_service - INFO - upload_video_file:82 - 上传会话初始化成功
2025-07-21 15:23:32,328 - src.services.ai_service - INFO - upload_video_file:104 - 视频文件上传成功: https://generativelanguage.googleapis.com/v1beta/files/mhq0z1vpojjk
2025-07-21 15:23:32,328 - src.services.ai_service - INFO - _wait_for_file_active:123 - 等待文件处理完成...
2025-07-21 15:23:32,951 - src.services.ai_service - INFO - _wait_for_file_active:139 - 文件状态: PROCESSING
2025-07-21 15:23:38,284 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:23:43,620 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:23:49,003 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:23:54,326 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:23:59,655 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:04,988 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:10,315 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:15,705 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:21,091 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:26,411 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:31,914 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:37,306 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:42,632 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:48,016 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:53,343 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:24:58,724 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:04,111 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:09,437 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:14,752 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:20,079 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:25,394 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:30,714 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:36,105 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:41,493 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
2025-07-21 15:25:46,810 - src.services.ai_service - WARNING - _wait_for_file_active:150 - Failed to check file status: 500
