from pydantic import BaseModel, Field, validator
from typing import List, Optional
from .base import BaseEntity, TimeRange

class NarrationVersion(BaseModel):
    version: str = Field(..., description="版本标识 v1/v2/v3")
    text: str = Field(..., description="旁白文本")
    word_count: int = Field(..., description="字数统计")
    estimated_duration: float = Field(..., description="预估时长")

class Storyboard(BaseEntity):
    scene_id: int = Field(..., description="分镜ID")
    source_time_range: TimeRange = Field(..., description="原视频时间范围")
    narration_script: str = Field(..., description="原始旁白脚本")
    optimized_narrations: List[NarrationVersion] = Field(
        default_factory=list, 
        description="优化后的多版本旁白"
    )
    video_segment_path: Optional[str] = Field(None, description="视频片段本地路径")
    
    @validator('scene_id')
    def validate_scene_id(cls, v):
        if v <= 0:
            raise ValueError('scene_id must be positive')
        return v

class StoryboardCollection(BaseModel):
    project_id: str = Field(..., description="项目ID")
    total_scenes: int = Field(..., description="总分镜数")
    storyboards: List[Storyboard] = Field(..., description="分镜列表")
    original_video_path: str = Field(..., description="原视频路径")
    
    @property
    def total_duration(self) -> float:
        return sum(sb.source_time_range.duration_seconds for sb in self.storyboards)
