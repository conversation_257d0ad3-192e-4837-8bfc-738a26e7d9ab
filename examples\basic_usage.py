#!/usr/bin/env python3
"""
Video Director Pro - 基础使用示例

这个示例展示了如何使用Video Director Pro进行基础的视频处理。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.workflow.engine import WorkflowEngine
from src.utils.logger import get_logger
from config import config_manager

logger = get_logger(__name__)

async def basic_video_processing():
    """基础视频处理示例"""
    
    print("🎬 Video Director Pro - 基础使用示例")
    print("=" * 50)
    
    # 创建工作流引擎
    engine = WorkflowEngine()
    
    # 添加进度回调
    def progress_callback(stage, progress, message, project_id):
        if progress >= 0:
            print(f"📊 [{stage}] {progress:.1%}: {message}")
        else:
            print(f"❌ [{stage}] 错误: {message}")
    
    engine.add_progress_callback(progress_callback)
    
    # 创建示例输入文件
    input_dir = Path("./data/input")
    input_dir.mkdir(parents=True, exist_ok=True)
    
    input_file = input_dir / "example_input.mp4"
    if not input_file.exists():
        print(f"📁 创建示例输入文件: {input_file}")
        with open(input_file, 'wb') as f:
            # 创建一个较大的模拟视频文件
            f.write(b"mock_video_data_for_example" * 10000)
    
    try:
        print(f"\n🎯 开始处理视频: {input_file}")
        print(f"   风格: 毒舌电影风格")
        print(f"   语言: 中文")
        print(f"   分镜数: 3")
        
        # 处理视频
        project = await engine.process_video(
            input_video_path=str(input_file),
            style="毒舌电影风格",
            language="中文",
            scene_count=3,
            project_name="基础示例项目"
        )
        
        print("\n🎉 处理完成！")
        print(f"   项目ID: {project.id}")
        print(f"   项目名称: {project.name}")
        print(f"   输出路径: {project.output_video_path}")
        print(f"   分镜数量: {len(project.segments)}")
        print(f"   处理状态: {'完成' if project.is_complete else '部分完成'}")
        
        # 显示处理统计
        if project.segments:
            total_duration = sum(seg.metadata.duration for seg in project.segments)
            print(f"   总时长: {total_duration:.2f}秒")
        
        return project
        
    except Exception as e:
        print(f"\n❌ 处理失败: {str(e)}")
        logger.error(f"Video processing failed: {str(e)}")
        return None

async def demonstrate_services():
    """演示各个服务的使用"""
    
    print("\n🔧 服务演示")
    print("=" * 30)
    
    # 演示配置管理
    print("\n1. 配置管理:")
    available_styles = config_manager.list_available_styles()
    print(f"   可用风格: {', '.join(available_styles)}")
    
    style_config = config_manager.get_style("毒舌电影风格")
    print(f"   风格描述: {style_config.description}")
    print(f"   默认音色: {style_config.voice_id}")
    
    # 演示AI服务
    print("\n2. AI服务:")
    from src.services.ai_service import AIAnalysisService
    
    ai_service = AIAnalysisService()
    print("   ✅ AI分析服务已初始化")
    
    # 演示TTS服务
    print("\n3. 语音合成服务:")
    from src.services.tts_service import TTSWorkflowService
    
    tts_service = TTSWorkflowService()
    print("   ✅ TTS工作流服务已初始化")
    
    # 演示视频服务
    print("\n4. 视频处理服务:")
    from src.services.video_service import VideoWorkflowService
    
    video_service = VideoWorkflowService()
    print("   ✅ 视频工作流服务已初始化")
    
    # 演示文件管理
    print("\n5. 文件管理服务:")
    from src.services.file_service import FileManager, CleanupService
    
    file_manager = FileManager()
    cleanup_service = CleanupService()
    
    disk_usage = cleanup_service.get_disk_usage()
    print("   磁盘使用情况:")
    for name, usage in disk_usage.items():
        print(f"     {name}: {usage['size_mb']:.2f} MB ({usage['file_count']} 文件)")

async def main():
    """主函数"""
    
    try:
        # 基础视频处理
        project = await basic_video_processing()
        
        # 服务演示
        await demonstrate_services()
        
        # 清理（如果处理成功）
        if project:
            print(f"\n🧹 清理项目临时文件: {project.id}")
            engine = WorkflowEngine()
            await engine.cleanup_project(project.id)
            print("   ✅ 清理完成")
        
        print("\n✨ 示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n💥 示例运行失败: {str(e)}")
        logger.error(f"Example failed: {str(e)}")

if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
