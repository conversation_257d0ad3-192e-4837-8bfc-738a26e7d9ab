import asyncio
import shutil
import hashlib
from typing import List, Dict, Any, Optional, Set
from pathlib import Path
import json
from datetime import datetime, timedelta

from ..utils.exceptions import FileManagementError
from ..utils.logger import get_logger
from config.settings import settings

logger = get_logger(__name__)

class FileManager:
    """文件管理器"""

    def __init__(self):
        self.data_dir = Path(settings.DATA_DIR)
        self.input_dir = Path(settings.INPUT_DIR)
        self.output_dir = Path(settings.OUTPUT_DIR)
        self.temp_dir = Path(settings.TEMP_DIR)
        self.cache_dir = Path(settings.CACHE_DIR)

        # 初始化目录结构
        self._init_directories()

    def _init_directories(self):
        """初始化目录结构"""
        directories = [
            self.data_dir,
            self.input_dir,
            self.output_dir,
            self.temp_dir,
            self.cache_dir,
            self.temp_dir / "segments",
            self.temp_dir / "audio",
            self.temp_dir / "processing"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {directory}")

    def create_project_workspace(self, project_id: str) -> Dict[str, Path]:
        """
        为项目创建工作空间

        Args:
            project_id: 项目ID

        Returns:
            工作空间路径字典
        """
        try:
            project_temp = self.temp_dir / project_id
            project_output = self.output_dir / project_id

            workspace = {
                "project_temp": project_temp,
                "project_output": project_output,
                "segments": project_temp / "segments",
                "audio": project_temp / "audio",
                "processing": project_temp / "processing",
                "final": project_output
            }

            # 创建所有目录
            for path in workspace.values():
                path.mkdir(parents=True, exist_ok=True)

            logger.info(f"Created workspace for project {project_id}")
            return workspace

        except Exception as e:
            logger.error(f"Failed to create workspace for project {project_id}: {str(e)}")
            raise FileManagementError(f"Failed to create project workspace: {str(e)}")

    def get_unique_filename(self, directory: Path, base_name: str, extension: str) -> Path:
        """
        获取唯一的文件名

        Args:
            directory: 目录路径
            base_name: 基础文件名
            extension: 文件扩展名

        Returns:
            唯一的文件路径
        """
        counter = 1
        while True:
            if counter == 1:
                filename = f"{base_name}.{extension}"
            else:
                filename = f"{base_name}_{counter}.{extension}"

            file_path = directory / filename
            if not file_path.exists():
                return file_path

            counter += 1

    def calculate_file_hash(self, file_path: Path) -> str:
        """
        计算文件哈希值

        Args:
            file_path: 文件路径

        Returns:
            文件MD5哈希值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Failed to calculate hash for {file_path}: {str(e)}")
            return ""

    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            文件信息字典
        """
        try:
            if not file_path.exists():
                return {}

            stat = file_path.stat()
            return {
                "path": str(file_path),
                "name": file_path.name,
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime),
                "modified": datetime.fromtimestamp(stat.st_mtime),
                "extension": file_path.suffix.lower(),
                "hash": self.calculate_file_hash(file_path)
            }
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {str(e)}")
            return {}

    async def copy_file_async(self, src: Path, dst: Path) -> Path:
        """
        异步复制文件

        Args:
            src: 源文件路径
            dst: 目标文件路径

        Returns:
            目标文件路径
        """
        try:
            # 确保目标目录存在
            dst.parent.mkdir(parents=True, exist_ok=True)

            # 在线程池中执行复制操作
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, shutil.copy2, str(src), str(dst))

            logger.debug(f"Copied file: {src} -> {dst}")
            return dst

        except Exception as e:
            logger.error(f"Failed to copy file {src} to {dst}: {str(e)}")
            raise FileManagementError(f"Failed to copy file: {str(e)}")

    async def move_file_async(self, src: Path, dst: Path) -> Path:
        """
        异步移动文件

        Args:
            src: 源文件路径
            dst: 目标文件路径

        Returns:
            目标文件路径
        """
        try:
            # 确保目标目录存在
            dst.parent.mkdir(parents=True, exist_ok=True)

            # 在线程池中执行移动操作
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, shutil.move, str(src), str(dst))

            logger.debug(f"Moved file: {src} -> {dst}")
            return dst

        except Exception as e:
            logger.error(f"Failed to move file {src} to {dst}: {str(e)}")
            raise FileManagementError(f"Failed to move file: {str(e)}")

class ProjectFileManager:
    """项目文件管理器"""

    def __init__(self, project_id: str):
        self.project_id = project_id
        self.file_manager = FileManager()
        self.workspace = self.file_manager.create_project_workspace(project_id)
        self.manifest_file = self.workspace["project_temp"] / "manifest.json"
        self.manifest = self._load_manifest()

    def _load_manifest(self) -> Dict[str, Any]:
        """加载项目清单"""
        try:
            if self.manifest_file.exists():
                with open(self.manifest_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    "project_id": self.project_id,
                    "created_at": datetime.now().isoformat(),
                    "files": {},
                    "stages": {}
                }
        except Exception as e:
            logger.error(f"Failed to load manifest: {str(e)}")
            return {"project_id": self.project_id, "files": {}, "stages": {}}

    def _save_manifest(self):
        """保存项目清单"""
        try:
            self.manifest["updated_at"] = datetime.now().isoformat()
            with open(self.manifest_file, 'w', encoding='utf-8') as f:
                json.dump(self.manifest, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save manifest: {str(e)}")

    def register_file(self, file_path: Path, category: str, metadata: Optional[Dict] = None) -> str:
        """
        注册文件到项目清单

        Args:
            file_path: 文件路径
            category: 文件类别
            metadata: 额外元数据

        Returns:
            文件ID
        """
        try:
            file_info = self.file_manager.get_file_info(file_path)
            file_id = f"{category}_{file_info.get('hash', 'unknown')[:8]}"

            self.manifest["files"][file_id] = {
                **file_info,
                "category": category,
                "metadata": metadata or {}
            }

            self._save_manifest()
            logger.debug(f"Registered file {file_id}: {file_path}")
            return file_id

        except Exception as e:
            logger.error(f"Failed to register file {file_path}: {str(e)}")
            raise FileManagementError(f"Failed to register file: {str(e)}")

    def get_files_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        按类别获取文件

        Args:
            category: 文件类别

        Returns:
            文件信息列表
        """
        return [
            {"id": file_id, **file_info}
            for file_id, file_info in self.manifest["files"].items()
            if file_info.get("category") == category
        ]

    def update_stage_status(self, stage: str, status: str, data: Optional[Dict] = None):
        """
        更新阶段状态

        Args:
            stage: 阶段名称
            status: 状态
            data: 额外数据
        """
        self.manifest["stages"][stage] = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "data": data or {}
        }
        self._save_manifest()

    def get_stage_status(self, stage: str) -> Optional[Dict[str, Any]]:
        """
        获取阶段状态

        Args:
            stage: 阶段名称

        Returns:
            阶段状态信息
        """
        return self.manifest["stages"].get(stage)

class CleanupService:
    """清理服务"""

    def __init__(self):
        self.file_manager = FileManager()

    async def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        清理临时文件

        Args:
            max_age_hours: 最大保留时间(小时)
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            temp_dir = self.file_manager.temp_dir

            cleaned_files = []
            cleaned_size = 0

            for file_path in temp_dir.rglob("*"):
                if file_path.is_file():
                    try:
                        stat = file_path.stat()
                        modified_time = datetime.fromtimestamp(stat.st_mtime)

                        if modified_time < cutoff_time:
                            file_size = stat.st_size
                            file_path.unlink()
                            cleaned_files.append(str(file_path))
                            cleaned_size += file_size
                    except Exception as e:
                        logger.warning(f"Failed to clean file {file_path}: {str(e)}")

            # 清理空目录
            for dir_path in temp_dir.rglob("*"):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    try:
                        dir_path.rmdir()
                    except Exception:
                        pass

            logger.info(f"Cleaned {len(cleaned_files)} files, freed {cleaned_size / 1024 / 1024:.2f} MB")

        except Exception as e:
            logger.error(f"Cleanup failed: {str(e)}")
            raise FileManagementError(f"Failed to cleanup temp files: {str(e)}")

    async def cleanup_project(self, project_id: str):
        """
        清理项目文件

        Args:
            project_id: 项目ID
        """
        try:
            project_temp = self.file_manager.temp_dir / project_id

            if project_temp.exists():
                # 在线程池中执行删除操作
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, shutil.rmtree, str(project_temp))
                logger.info(f"Cleaned project {project_id} temp files")

        except Exception as e:
            logger.error(f"Failed to cleanup project {project_id}: {str(e)}")
            raise FileManagementError(f"Failed to cleanup project: {str(e)}")

    def get_disk_usage(self) -> Dict[str, Dict[str, Any]]:
        """
        获取磁盘使用情况

        Returns:
            磁盘使用情况字典
        """
        try:
            usage = {}

            for name, path in [
                ("data", self.file_manager.data_dir),
                ("temp", self.file_manager.temp_dir),
                ("output", self.file_manager.output_dir),
                ("cache", self.file_manager.cache_dir)
            ]:
                if path.exists():
                    total_size = sum(
                        f.stat().st_size for f in path.rglob("*") if f.is_file()
                    )
                    file_count = sum(1 for f in path.rglob("*") if f.is_file())

                    usage[name] = {
                        "path": str(path),
                        "size_bytes": total_size,
                        "size_mb": total_size / 1024 / 1024,
                        "file_count": file_count
                    }
                else:
                    usage[name] = {
                        "path": str(path),
                        "size_bytes": 0,
                        "size_mb": 0,
                        "file_count": 0
                    }

            return usage

        except Exception as e:
            logger.error(f"Failed to get disk usage: {str(e)}")
            return {}
