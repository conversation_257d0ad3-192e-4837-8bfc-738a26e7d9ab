from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="video-director-pro",
    version="1.0.0",
    author="Video Director Pro Team",
    author_email="<EMAIL>",
    description="全自动视频剪辑系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/video-director-pro",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "video-director=cli.main:cli",
        ],
    },
    include_package_data=True,
    package_data={
        "config": ["*.py", "styles/*.py", "prompts/*.py"],
    },
)
