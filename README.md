# Video Director Pro

🎬 全自动视频剪辑系统 - Python异步实现版本

## 功能特性

- 🤖 **AI驱动**: 使用Google Gemini 2.5 Pro进行视频内容分析
- 🎵 **智能配音**: Fish Audio高质量语音合成，支持多语言
- ⚡ **异步处理**: 高性能并发处理，支持大规模视频处理
- 🎨 **多种风格**: 多种预设剪辑风格，满足不同需求
- 🔧 **本地处理**: 使用本地FFmpeg，无需云服务依赖
- 📊 **实时监控**: 完整的进度跟踪和错误处理

## 快速开始

### 1. 环境要求

- Python 3.8+
- FFmpeg 4.0+
- 8GB+ RAM (推荐16GB)

### 2. 安装

```bash
# 克隆项目
git clone https://github.com/your-repo/video-director-pro.git
cd video-director-pro

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，填入API密钥
# 必需的API密钥：
# - GEMINI_API_KEY: Google Gemini API密钥
# - FISH_AUDIO_API_KEY: Fish Audio API密钥
```

### 4. 测试安装

```bash
# 测试基础功能
python cli/main.py test

# 查看系统状态
python cli/main.py status

# 查看可用风格
python cli/main.py styles
```

## 项目结构

```
video_director_pro/
├── src/                    # 源代码
│   ├── models/            # 数据模型
│   ├── services/          # 服务层
│   ├── workflow/          # 工作流引擎
│   ├── utils/             # 工具类
│   └── api/               # API接口
├── config/                # 配置文件
├── cli/                   # 命令行界面
├── tests/                 # 测试代码
├── data/                  # 数据目录
└── scripts/               # 脚本文件
```

## 使用方法

### 基础测试

```bash
# 测试系统状态
python cli/main.py status

# 查看可用风格
python cli/main.py styles

# 测试基础功能
python cli/main.py test

# 测试AI服务
python cli/main.py test-ai

# 测试语音合成服务
python cli/main.py test-tts

# 测试视频处理服务
python cli/main.py test-video

# 测试文件管理服务
python cli/main.py test-files
```

### 完整视频处理

```bash
# 处理视频（使用默认参数）
python cli/main.py process

# 自定义参数处理
python cli/main.py process input_video.mp4 \
  --style "毒舌电影风格" \
  --language "中文" \
  --scenes 5 \
  --name "我的项目"
```

### 可用的剪辑风格

- **毒舌电影风格**: 犀利、讽刺、幽默的电影解说风格
- **顾我电影风格**: 深度思考、情感洞察的电影解说风格
- **俏皮自然纪录片风格**: 轻松幽默、贴近自然的纪录片解说风格
- **商品评测风格**: 专业客观、实用导向的商品评测解说风格

## 开发状态

当前版本：v1.0.0-beta

- ✅ 项目架构和基础结构
- ✅ 核心数据模型
- ✅ 配置管理系统
- ✅ 错误处理和日志系统
- ✅ AI服务接口 (模拟实现)
- ✅ 语音合成服务 (模拟实现)
- ✅ 视频处理服务 (模拟实现)
- ✅ 工作流引擎 (完整实现)
- ✅ 文件管理系统
- ✅ 命令行界面

## 技术特性

### 异步架构
- 基于asyncio的高性能异步处理
- 支持并发的视频片段处理
- 智能的任务调度和资源管理

### 模块化设计
- 清晰的服务层分离
- 可扩展的风格配置系统
- 完善的错误处理和重试机制

### 智能处理
- AI驱动的视频内容分析
- 多版本旁白生成和智能选择
- 自动音画同步和调速处理

## 注意事项

### 当前限制
1. **模拟实现**: 当前版本使用模拟的AI和TTS服务，需要真实API密钥才能正常工作
2. **FFmpeg依赖**: 需要系统安装FFmpeg才能进行真实的视频处理
3. **API配置**: 需要配置Gemini和Fish Audio的API密钥

### 生产部署
要在生产环境中使用，需要：
1. 获取Google Gemini API密钥
2. 获取Fish Audio API密钥
3. 安装并配置FFmpeg
4. 根据需要调整并发参数和资源限制

## 许可证

MIT License
