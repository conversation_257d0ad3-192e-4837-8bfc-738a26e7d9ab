import asyncio
import subprocess
import json
import shutil
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

from ..models.video import VideoSegment, VideoMetadata, ProcessedSegment
from ..models.base import TimeRange
from ..models.audio import AudioFile
from ..utils.exceptions import VideoProcessingError, FFmpegError
from ..utils.logger import get_logger
from ..utils.retry import ffmpeg_retry
from config.settings import settings

logger = get_logger(__name__)

class FFmpegService:
    """FFmpeg服务封装"""

    def __init__(self):
        self.ffmpeg_path = settings.FFMPEG_PATH
        self.ffprobe_path = settings.FFPROBE_PATH
        self._validate_ffmpeg()

    def _validate_ffmpeg(self):
        """验证FFmpeg是否可用"""
        try:
            subprocess.run([self.ffmpeg_path, "-version"],
                         capture_output=True, check=True)
            subprocess.run([self.ffprobe_path, "-version"],
                         capture_output=True, check=True)
            logger.info("FFmpeg validation successful")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("FFmpeg not found, using mock implementation")
            # 在开发环境中使用模拟实现

    async def get_video_metadata(self, video_path: str) -> VideoMetadata:
        """
        获取视频元数据

        Args:
            video_path: 视频文件路径

        Returns:
            视频元数据
        """
        try:
            logger.info(f"获取视频元数据: {video_path}")

            # 模拟视频元数据 - 在实际实现中使用ffprobe
            metadata = VideoMetadata(
                width=1920,
                height=1080,
                fps=25.0,
                duration=30.0,  # 假设30秒视频
                codec="h264",
                bitrate=2000000,
                file_size=10 * 1024 * 1024  # 10MB
            )

            logger.info(f"视频元数据获取成功: {metadata.width}x{metadata.height}, {metadata.duration}s")
            return metadata

        except Exception as e:
            logger.error(f"Failed to get video metadata: {str(e)}")
            raise VideoProcessingError(f"Failed to get video metadata: {str(e)}")

    async def split_video(
        self,
        input_path: str,
        time_ranges: List[TimeRange],
        output_dir: str
    ) -> List[VideoSegment]:
        """
        按时间范围拆分视频

        Args:
            input_path: 输入视频路径
            time_ranges: 时间范围列表
            output_dir: 输出目录

        Returns:
            视频片段列表
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            logger.info(f"开始拆分视频: {input_path} -> {len(time_ranges)} 个片段")

            # 获取原视频元数据
            original_metadata = await self.get_video_metadata(input_path)

            # 创建并发任务
            tasks = []
            for i, time_range in enumerate(time_ranges):
                segment_path = output_path / f"segment_{i+1:03d}.mp4"
                task = self._extract_segment(
                    input_path=input_path,
                    output_path=str(segment_path),
                    start_time=time_range.start_time,
                    duration=time_range.duration_seconds,
                    scene_id=i+1
                )
                tasks.append(task)

            # 限制并发数量
            semaphore = asyncio.Semaphore(settings.MAX_CONCURRENT_TASKS)

            async def bounded_extraction(task):
                async with semaphore:
                    return await task

            # 执行并发拆分
            segments = await asyncio.gather(
                *[bounded_extraction(task) for task in tasks],
                return_exceptions=True
            )

            # 处理结果
            successful_segments = []
            for i, result in enumerate(segments):
                if isinstance(result, Exception):
                    logger.error(f"Failed to extract segment {i+1}: {str(result)}")
                else:
                    successful_segments.append(result)

            if not successful_segments:
                raise VideoProcessingError("All video splitting attempts failed")

            logger.info(f"视频拆分完成: {len(successful_segments)} 个片段")
            return successful_segments

        except Exception as e:
            logger.error(f"Video splitting failed: {str(e)}")
            raise VideoProcessingError(f"Failed to split video: {str(e)}")

    @ffmpeg_retry
    async def _extract_segment(
        self,
        input_path: str,
        output_path: str,
        start_time: str,
        duration: float,
        scene_id: int
    ) -> VideoSegment:
        """
        提取单个视频片段

        Args:
            input_path: 输入视频路径
            output_path: 输出路径
            start_time: 开始时间
            duration: 时长
            scene_id: 分镜ID

        Returns:
            视频片段对象
        """
        try:
            logger.info(f"提取视频片段 {scene_id}: {start_time} -> {duration}s")

            # 模拟视频片段提取 - 在实际实现中使用ffmpeg
            # 创建一个模拟的视频文件
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'wb') as f:
                f.write(b"mock_video_segment_data_" + str(scene_id).encode())

            # 获取片段元数据
            metadata = VideoMetadata(
                width=1920,
                height=1080,
                fps=25.0,
                duration=duration,
                codec="h264",
                bitrate=2000000,
                file_size=int(duration * 1024 * 1024)  # 估算文件大小
            )

            # 计算结束时间
            start_seconds = self._time_to_seconds(start_time)
            end_seconds = start_seconds + duration
            end_time = self._seconds_to_time(end_seconds)

            # 创建时间范围
            time_range = TimeRange(
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration
            )

            # 创建视频片段对象
            segment = VideoSegment(
                scene_id=scene_id,
                source_video_path=input_path,
                segment_path=output_path,
                time_range=time_range,
                metadata=metadata
            )

            logger.info(f"视频片段提取完成: {output_path}")
            return segment

        except Exception as e:
            logger.error(f"Failed to extract segment {scene_id}: {str(e)}")
            raise VideoProcessingError(f"Failed to extract video segment: {str(e)}")

    @ffmpeg_retry
    async def adjust_video_speed(
        self,
        input_path: str,
        output_path: str,
        speed_ratio: float
    ) -> str:
        """
        调整视频播放速度

        Args:
            input_path: 输入视频路径
            output_path: 输出路径
            speed_ratio: 速度比例 (>1加速, <1减速)

        Returns:
            输出文件路径
        """
        try:
            logger.info(f"调整视频速度: {input_path} -> {speed_ratio}x")

            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            # 模拟视频调速 - 在实际实现中使用ffmpeg
            with open(input_path, 'rb') as src:
                content = src.read()

            # 创建调速后的文件
            with open(output_path, 'wb') as dst:
                dst.write(b"speed_adjusted_" + content)

            logger.info(f"视频调速完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Speed adjustment failed: {str(e)}")
            raise VideoProcessingError(f"Failed to adjust video speed: {str(e)}")

    @ffmpeg_retry
    async def merge_audio_video(
        self,
        video_path: str,
        audio_path: str,
        output_path: str
    ) -> str:
        """
        合并音频和视频

        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出路径

        Returns:
            输出文件路径
        """
        try:
            logger.info(f"合并音视频: {video_path} + {audio_path} -> {output_path}")

            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            # 模拟音视频合并 - 在实际实现中使用ffmpeg
            with open(video_path, 'rb') as video_file:
                video_content = video_file.read()

            with open(audio_path, 'rb') as audio_file:
                audio_content = audio_file.read()

            # 创建合并后的文件
            with open(output_path, 'wb') as output_file:
                output_file.write(b"merged_" + video_content + audio_content)

            logger.info(f"音视频合并完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Audio-video merge failed: {str(e)}")
            raise VideoProcessingError(f"Failed to merge audio and video: {str(e)}")

    @ffmpeg_retry
    async def concatenate_videos(
        self,
        video_paths: List[str],
        output_path: str
    ) -> str:
        """
        拼接多个视频文件

        Args:
            video_paths: 视频文件路径列表
            output_path: 输出路径

        Returns:
            输出文件路径
        """
        try:
            if not video_paths:
                raise VideoProcessingError("No video files to concatenate")

            logger.info(f"拼接视频: {len(video_paths)} 个文件 -> {output_path}")

            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            # 模拟视频拼接 - 在实际实现中使用ffmpeg
            concatenated_content = b"concatenated_video_"

            for video_path in video_paths:
                if Path(video_path).exists():
                    with open(video_path, 'rb') as f:
                        concatenated_content += f.read()

            # 创建拼接后的文件
            with open(output_path, 'wb') as f:
                f.write(concatenated_content)

            logger.info(f"视频拼接完成: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Video concatenation failed: {str(e)}")
            raise VideoProcessingError(f"Failed to concatenate videos: {str(e)}")

    def _time_to_seconds(self, time_str: str) -> float:
        """将时间字符串转换为秒数"""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                h, m, s = parts
                return float(h) * 3600 + float(m) * 60 + float(s)
            elif len(parts) == 2:
                m, s = parts
                return float(m) * 60 + float(s)
            else:
                return float(parts[0])
        except:
            return 0.0

    def _seconds_to_time(self, seconds: float) -> str:
        """将秒数转换为时间字符串"""
        h = int(seconds // 3600)
        m = int((seconds % 3600) // 60)
        s = seconds % 60
        return f"{h:02d}:{m:02d}:{s:06.3f}"

class VideoWorkflowService:
    """视频处理工作流服务"""

    def __init__(self):
        self.ffmpeg = FFmpegService()

    async def process_video_segment(
        self,
        segment: VideoSegment,
        audio_file: AudioFile,
        output_dir: str
    ) -> ProcessedSegment:
        """
        处理单个视频片段（调速+合成音频）

        Args:
            segment: 视频片段
            audio_file: 音频文件
            output_dir: 输出目录

        Returns:
            处理后的视频片段
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # 计算速度比例
            video_duration = segment.metadata.duration
            audio_duration = audio_file.metadata.duration
            speed_ratio = video_duration / audio_duration

            logger.info(f"处理视频片段 {segment.scene_id}: 速度比例 {speed_ratio:.3f}")

            # 调整视频速度
            speed_adjusted_path = output_path / f"speed_adjusted_{segment.scene_id}.mp4"
            await self.ffmpeg.adjust_video_speed(
                input_path=segment.segment_path,
                output_path=str(speed_adjusted_path),
                speed_ratio=speed_ratio
            )

            # 合并音频和视频
            final_path = output_path / f"final_segment_{segment.scene_id}.mp4"
            await self.ffmpeg.merge_audio_video(
                video_path=str(speed_adjusted_path),
                audio_path=audio_file.file_path,
                output_path=str(final_path)
            )

            # 创建处理后的片段对象
            processed_segment = ProcessedSegment(
                original_segment=segment,
                audio_file=audio_file,
                speed_ratio=speed_ratio,
                synced_video_path=str(speed_adjusted_path),
                final_segment_path=str(final_path)
            )

            logger.info(f"视频片段处理完成: {final_path}")
            return processed_segment

        except Exception as e:
            logger.error(f"Failed to process video segment {segment.scene_id}: {str(e)}")
            raise VideoProcessingError(f"Failed to process video segment: {str(e)}")

    async def process_all_segments(
        self,
        segments_and_audio: List[Tuple[VideoSegment, AudioFile]],
        output_dir: str
    ) -> List[ProcessedSegment]:
        """
        处理所有视频片段

        Args:
            segments_and_audio: (视频片段, 音频文件) 元组列表
            output_dir: 输出目录

        Returns:
            处理后的视频片段列表
        """
        try:
            logger.info(f"开始处理 {len(segments_and_audio)} 个视频片段")

            # 创建并发任务
            tasks = []
            for segment, audio_file in segments_and_audio:
                task = self.process_video_segment(
                    segment=segment,
                    audio_file=audio_file,
                    output_dir=output_dir
                )
                tasks.append(task)

            # 限制并发数量
            semaphore = asyncio.Semaphore(settings.MAX_CONCURRENT_TASKS)

            async def bounded_processing(task):
                async with semaphore:
                    return await task

            # 执行并发处理
            results = await asyncio.gather(
                *[bounded_processing(task) for task in tasks],
                return_exceptions=True
            )

            # 处理结果
            processed_segments = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to process segment {i+1}: {str(result)}")
                    raise VideoProcessingError(f"Segment processing failed: {str(result)}")
                else:
                    processed_segments.append(result)

            # 按scene_id排序
            processed_segments.sort(key=lambda x: x.original_segment.scene_id)

            logger.info(f"所有视频片段处理完成: {len(processed_segments)} 个")
            return processed_segments

        except Exception as e:
            logger.error(f"Failed to process all segments: {str(e)}")
            raise VideoProcessingError(f"Failed to process all video segments: {str(e)}")

    async def create_final_video(
        self,
        processed_segments: List[ProcessedSegment],
        output_path: str
    ) -> str:
        """
        创建最终视频

        Args:
            processed_segments: 处理后的视频片段列表
            output_path: 输出路径

        Returns:
            最终视频路径
        """
        try:
            # 提取所有最终片段路径
            segment_paths = [seg.final_segment_path for seg in processed_segments]

            logger.info(f"创建最终视频: {len(segment_paths)} 个片段 -> {output_path}")

            # 拼接视频
            final_path = await self.ffmpeg.concatenate_videos(
                video_paths=segment_paths,
                output_path=output_path
            )

            logger.info(f"最终视频创建完成: {final_path}")
            return final_path

        except Exception as e:
            logger.error(f"Failed to create final video: {str(e)}")
            raise VideoProcessingError(f"Failed to create final video: {str(e)}")
