from abc import ABC, abstractmethod
from pydantic import BaseModel, Field
from typing import Dict, List, Optional

class StyleConfig(BaseModel):
    """剪辑风格配置基类"""
    name: str = Field(..., description="风格名称")
    description: str = Field(..., description="风格描述")
    channel_name: str = Field(..., description="频道名称")
    voice_id: str = Field(..., description="默认音色ID")
    language_voices: Dict[str, str] = Field(
        default_factory=dict, 
        description="多语言音色映射"
    )
    
    # 时长配置
    min_scene_duration: float = Field(default=6.0, description="最小分镜时长")
    max_scene_duration: float = Field(default=12.0, description="最大分镜时长")
    
    # 旁白配置
    narration_speed_multipliers: List[float] = Field(
        default=[4.0, 4.5, 5.5], 
        description="旁白速度倍数(字/秒)"
    )
    
    @abstractmethod
    def get_analysis_prompt(self, **kwargs) -> str:
        """获取视频分析提示词"""
        pass
    
    @abstractmethod
    def get_sync_prompt(self, **kwargs) -> str:
        """获取音画同步提示词"""
        pass
    
    def get_voice_id(self, language: str) -> str:
        """根据语言获取音色ID"""
        return self.language_voices.get(language, self.voice_id)

class StyleRegistry:
    """风格注册表"""
    _styles: Dict[str, StyleConfig] = {}
    
    @classmethod
    def register(cls, style: StyleConfig):
        """注册风格"""
        cls._styles[style.name] = style
    
    @classmethod
    def get(cls, name: str) -> Optional[StyleConfig]:
        """获取风格配置"""
        return cls._styles.get(name)
    
    @classmethod
    def list_styles(cls) -> List[str]:
        """列出所有风格"""
        return list(cls._styles.keys())
    
    @classmethod
    def get_all(cls) -> Dict[str, StyleConfig]:
        """获取所有风格配置"""
        return cls._styles.copy()
