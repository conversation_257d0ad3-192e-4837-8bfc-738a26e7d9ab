# Video Director Pro - API 文档

## 概述

Video Director Pro 提供了完整的Python API，支持异步视频处理工作流。

## 核心类和接口

### 工作流引擎

```python
from src.workflow.engine import WorkflowEngine

# 创建工作流引擎
engine = WorkflowEngine()

# 添加进度回调
def progress_callback(stage, progress, message, project_id):
    print(f"[{stage}] {progress:.1%}: {message}")

engine.add_progress_callback(progress_callback)

# 处理视频
project = await engine.process_video(
    input_video_path="input.mp4",
    style="毒舌电影风格",
    language="中文",
    scene_count=10,
    project_name="我的项目"
)
```

### AI服务

```python
from src.services.ai_service import AIAnalysisService
from config import config_manager

# 创建AI服务
ai_service = AIAnalysisService()

# 获取风格配置
style_config = config_manager.get_style("毒舌电影风格")

# 分析视频
storyboard_collection = await ai_service.analyze_video_with_style(
    video_path="input.mp4",
    style_config=style_config,
    scene_count=10,
    language="中文"
)

# 优化旁白
narration_versions = await ai_service.optimize_scene_narration(
    storyboard=storyboard,
    video_segment_path="segment.mp4",
    style_config=style_config,
    language="中文"
)
```

### 语音合成服务

```python
from src.services.tts_service import TTSWorkflowService

# 创建TTS服务
tts_service = TTSWorkflowService()

# 处理场景音频
audio_selection = await tts_service.process_scene_audio(
    narration_versions=narration_versions,
    voice_id="voice_id",
    language="中文",
    scene_id=1,
    target_duration=8.5
)
```

### 视频处理服务

```python
from src.services.video_service import VideoWorkflowService

# 创建视频服务
video_service = VideoWorkflowService()

# 处理视频片段
processed_segment = await video_service.process_video_segment(
    segment=video_segment,
    audio_file=audio_file,
    output_dir="./output"
)

# 创建最终视频
final_path = await video_service.create_final_video(
    processed_segments=processed_segments,
    output_path="final.mp4"
)
```

### 文件管理服务

```python
from src.services.file_service import ProjectFileManager, CleanupService

# 创建项目文件管理器
file_manager = ProjectFileManager("project_id")

# 注册文件
file_id = file_manager.register_file(
    file_path=Path("file.mp4"),
    category="video",
    metadata={"description": "输入视频"}
)

# 更新阶段状态
file_manager.update_stage_status("processing", "completed")

# 清理服务
cleanup_service = CleanupService()
await cleanup_service.cleanup_temp_files(max_age_hours=24)
```

## 数据模型

### 视频项目

```python
from src.models.video import VideoProject

project = VideoProject(
    name="我的项目",
    input_video_path="input.mp4",
    style="毒舌电影风格",
    language="中文",
    scene_count=10
)
```

### 分镜集合

```python
from src.models.storyboard import StoryboardCollection, Storyboard
from src.models.base import TimeRange

# 时间范围
time_range = TimeRange(
    start_time="00:00:10.500",
    end_time="00:00:18.200",
    duration_seconds=7.7
)

# 分镜
storyboard = Storyboard(
    scene_id=1,
    source_time_range=time_range,
    narration_script="旁白文本"
)
```

### 音频文件

```python
from src.models.audio import AudioFile, AudioMetadata

# 音频元数据
metadata = AudioMetadata(
    duration=7.5,
    sample_rate=44100,
    channels=1,
    bitrate=128000,
    format="mp3",
    file_size=1024*1024
)

# 音频文件
audio_file = AudioFile(
    file_path="audio.mp3",
    narration_text="旁白文本",
    voice_id="voice_id",
    language="中文",
    metadata=metadata
)
```

## 配置管理

### 风格配置

```python
from config import config_manager

# 列出可用风格
styles = config_manager.list_available_styles()

# 获取风格配置
style_config = config_manager.get_style("毒舌电影风格")

# 获取音色ID
voice_id = style_config.get_voice_id("中文")
```

### 自定义风格

```python
from config.styles.base import StyleConfig, StyleRegistry

class MyCustomStyle(StyleConfig):
    name = "我的风格"
    description = "自定义风格描述"
    voice_id = "my_voice_id"
    
    def get_analysis_prompt(self, **kwargs):
        return "自定义分析提示词"
    
    def get_sync_prompt(self, **kwargs):
        return "自定义同步提示词"

# 注册风格
StyleRegistry.register(MyCustomStyle())
```

## 错误处理

```python
from src.utils.exceptions import *

try:
    result = await some_operation()
except VideoDirectorError as e:
    print(f"错误代码: {e.error_code}")
    print(f"错误信息: {e.message}")
    print(f"详细信息: {e.details}")
except RetryableError as e:
    print(f"可重试错误，建议等待 {e.retry_after} 秒后重试")
```

## 日志记录

```python
from src.utils.logger import get_logger, get_project_logger

# 基础日志器
logger = get_logger(__name__)
logger.info("处理开始")

# 项目日志器
project_logger = get_project_logger(__name__, "project_id")
project_logger.info("项目处理开始")
```

## 重试机制

```python
from src.utils.retry import retry, api_retry

# 自定义重试
@retry(max_attempts=3, delay=1.0, backoff=2.0)
async def my_function():
    # 可能失败的操作
    pass

# API重试（预配置）
@api_retry
async def api_call():
    # API调用
    pass
```

## 最佳实践

### 1. 异步处理
- 始终使用 `await` 调用异步方法
- 合理设置并发限制
- 使用进度回调监控处理状态

### 2. 错误处理
- 捕获特定的异常类型
- 实现适当的重试逻辑
- 记录详细的错误信息

### 3. 资源管理
- 及时清理临时文件
- 监控内存和磁盘使用
- 合理配置并发参数

### 4. 性能优化
- 使用批量处理减少API调用
- 缓存重复的计算结果
- 优化文件I/O操作

## 示例：完整处理流程

```python
import asyncio
from src.workflow.engine import WorkflowEngine

async def main():
    # 创建引擎
    engine = WorkflowEngine()
    
    # 添加进度监控
    def progress_callback(stage, progress, message, project_id):
        print(f"[{project_id}] {stage}: {progress:.1%} - {message}")
    
    engine.add_progress_callback(progress_callback)
    
    try:
        # 处理视频
        project = await engine.process_video(
            input_video_path="input.mp4",
            style="毒舌电影风格",
            language="中文",
            scene_count=5,
            project_name="示例项目"
        )
        
        print(f"处理完成: {project.output_video_path}")
        
    except Exception as e:
        print(f"处理失败: {e}")
    
    finally:
        # 清理资源
        await engine.cleanup_project(project.id)

# 运行
asyncio.run(main())
```
