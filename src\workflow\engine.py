import asyncio
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
import uuid

from ..models.video import VideoProject, VideoSegment, ProcessedSegment
from ..models.storyboard import StoryboardCollection, Storyboard
from ..models.audio import AudioSelection
from ..services.ai_service import AIAnalysisService
from ..services.tts_service import TTSWorkflowService
from ..services.video_service import VideoWorkflowService
from ..services.file_service import ProjectFileManager, CleanupService
from ..utils.exceptions import WorkflowError
from ..utils.logger import get_project_logger
from config import config_manager

class WorkflowEngine:
    """主工作流引擎"""
    
    def __init__(self):
        # 初始化服务
        self.ai_service = AIAnalysisService()
        self.tts_service = TTSWorkflowService()
        self.video_service = VideoWorkflowService()
        self.cleanup_service = CleanupService()
        
        # 进度回调
        self.progress_callbacks: List[Callable] = []
    
    def add_progress_callback(self, callback: Callable):
        """添加进度回调函数"""
        self.progress_callbacks.append(callback)
    
    async def _notify_progress(self, stage: str, progress: float, message: str, project_id: str):
        """通知进度更新"""
        for callback in self.progress_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(stage, progress, message, project_id)
                else:
                    callback(stage, progress, message, project_id)
            except Exception as e:
                # 进度回调失败不应该影响主流程
                pass
    
    async def process_video(
        self,
        input_video_path: str,
        style: str,
        language: str = "中文",
        scene_count: int = 10,
        custom_script: Optional[str] = None,
        project_name: Optional[str] = None
    ) -> VideoProject:
        """
        处理视频的主工作流
        
        Args:
            input_video_path: 输入视频路径
            style: 剪辑风格
            language: 语言
            scene_count: 分镜数量
            custom_script: 自定义文案
            project_name: 项目名称
            
        Returns:
            完成的视频项目
        """
        # 创建项目
        project_id = str(uuid.uuid4())
        logger = get_project_logger(__name__, project_id)
        
        try:
            logger.info(f"开始处理视频项目: {project_name or 'Untitled'}")
            
            # 创建项目对象
            project = VideoProject(
                id=project_id,
                name=project_name or f"Project_{project_id[:8]}",
                input_video_path=input_video_path,
                style=style,
                language=language,
                scene_count=scene_count,
                custom_script=custom_script
            )
            
            # 创建项目文件管理器
            file_manager = ProjectFileManager(project_id)
            
            # 获取风格配置
            style_config = config_manager.get_style(style)
            if not style_config:
                raise WorkflowError(f"Unknown style: {style}")
            
            await self._notify_progress("initialization", 0.05, "项目初始化完成", project_id)
            
            # 阶段1: 视频分析和分镜生成
            logger.info("阶段1: 开始视频分析")
            storyboard_collection = await self.ai_service.analyze_video_with_style(
                video_path=input_video_path,
                style_config=style_config,
                scene_count=scene_count,
                language=language,
                custom_script=custom_script
            )
            
            file_manager.update_stage_status("video_analysis", "completed", {
                "storyboard_count": len(storyboard_collection.storyboards)
            })
            
            await self._notify_progress("video_analysis", 0.15, f"视频分析完成，生成{len(storyboard_collection.storyboards)}个分镜", project_id)
            
            # 阶段2: 视频拆条
            logger.info("阶段2: 开始视频拆条")
            time_ranges = [sb.source_time_range for sb in storyboard_collection.storyboards]
            video_segments = await self.video_service.ffmpeg.split_video(
                input_path=input_video_path,
                time_ranges=time_ranges,
                output_dir=str(file_manager.workspace["segments"])
            )
            
            project.segments = video_segments
            file_manager.update_stage_status("video_splitting", "completed", {
                "segment_count": len(video_segments)
            })
            
            await self._notify_progress("video_splitting", 0.30, f"视频拆条完成，生成{len(video_segments)}个片段", project_id)
            
            # 阶段3: 旁白优化和语音合成
            logger.info("阶段3: 开始旁白优化和语音合成")
            audio_selections = await self._process_audio_generation(
                storyboard_collection=storyboard_collection,
                video_segments=video_segments,
                style_config=style_config,
                language=language,
                file_manager=file_manager,
                project_id=project_id
            )
            
            await self._notify_progress("audio_generation", 0.60, f"语音合成完成，生成{len(audio_selections)}个音频", project_id)
            
            # 阶段4: 视频处理和音画同步
            logger.info("阶段4: 开始视频处理和音画同步")
            processed_segments = await self._process_video_sync(
                video_segments=video_segments,
                audio_selections=audio_selections,
                file_manager=file_manager,
                project_id=project_id
            )
            
            project.processed_segments = processed_segments
            file_manager.update_stage_status("video_processing", "completed", {
                "processed_count": len(processed_segments)
            })
            
            await self._notify_progress("video_processing", 0.85, f"视频处理完成，处理{len(processed_segments)}个片段", project_id)
            
            # 阶段5: 最终视频合成
            logger.info("阶段5: 开始最终视频合成")
            output_path = file_manager.workspace["final"] / f"{project.name}.mp4"
            final_video_path = await self.video_service.create_final_video(
                processed_segments=processed_segments,
                output_path=str(output_path)
            )
            
            project.output_video_path = final_video_path
            file_manager.update_stage_status("final_composition", "completed", {
                "output_path": final_video_path
            })
            
            await self._notify_progress("final_composition", 1.0, "视频处理完成", project_id)
            
            logger.info(f"视频项目处理完成: {final_video_path}")
            return project
            
        except Exception as e:
            logger.error(f"视频处理失败: {str(e)}")
            file_manager.update_stage_status("error", "failed", {"error": str(e)})
            await self._notify_progress("error", -1, f"处理失败: {str(e)}", project_id)
            raise WorkflowError(f"Video processing failed: {str(e)}")
    
    async def _process_audio_generation(
        self,
        storyboard_collection: StoryboardCollection,
        video_segments: List[VideoSegment],
        style_config,
        language: str,
        file_manager: ProjectFileManager,
        project_id: str
    ) -> List[AudioSelection]:
        """处理音频生成阶段"""
        logger = get_project_logger(__name__, project_id)
        
        try:
            # 为每个分镜优化旁白
            optimized_storyboards = []
            for i, storyboard in enumerate(storyboard_collection.storyboards):
                if i < len(video_segments):
                    segment = video_segments[i]
                    
                    # 优化旁白
                    narration_versions = await self.ai_service.optimize_scene_narration(
                        storyboard=storyboard,
                        video_segment_path=segment.segment_path,
                        style_config=style_config,
                        language=language
                    )
                    
                    storyboard.optimized_narrations = narration_versions
                    optimized_storyboards.append(storyboard)
            
            # 准备音频生成数据
            scenes_data = []
            voice_id = style_config.get_voice_id(language)
            
            for storyboard in optimized_storyboards:
                scenes_data.append({
                    "narration_versions": storyboard.optimized_narrations,
                    "scene_id": storyboard.scene_id,
                    "target_duration": storyboard.source_time_range.duration_seconds
                })
            
            # 并发生成所有音频
            audio_selections = await self.tts_service.process_all_scenes_audio(
                scenes_data=scenes_data,
                voice_id=voice_id,
                language=language
            )
            
            file_manager.update_stage_status("audio_generation", "completed", {
                "audio_count": len(audio_selections)
            })
            
            return audio_selections
            
        except Exception as e:
            logger.error(f"音频生成失败: {str(e)}")
            raise WorkflowError(f"Audio generation failed: {str(e)}")
    
    async def _process_video_sync(
        self,
        video_segments: List[VideoSegment],
        audio_selections: List[AudioSelection],
        file_manager: ProjectFileManager,
        project_id: str
    ) -> List[ProcessedSegment]:
        """处理视频同步阶段"""
        logger = get_project_logger(__name__, project_id)
        
        try:
            # 配对视频片段和音频
            segments_and_audio = []
            for i, segment in enumerate(video_segments):
                if i < len(audio_selections):
                    audio_selection = audio_selections[i]
                    segments_and_audio.append((segment, audio_selection.selected.audio_file))
            
            # 并发处理所有片段
            processed_segments = await self.video_service.process_all_segments(
                segments_and_audio=segments_and_audio,
                output_dir=str(file_manager.workspace["processing"])
            )
            
            return processed_segments
            
        except Exception as e:
            logger.error(f"视频同步失败: {str(e)}")
            raise WorkflowError(f"Video sync failed: {str(e)}")
    
    async def cleanup_project(self, project_id: str):
        """清理项目临时文件"""
        try:
            await self.cleanup_service.cleanup_project(project_id)
        except Exception as e:
            # 清理失败不应该影响主流程
            pass
