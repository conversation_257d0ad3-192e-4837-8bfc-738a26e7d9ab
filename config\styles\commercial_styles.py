from .base import StyleConfig, StyleRegistry

class ProductReviewStyle(StyleConfig):
    """商品评测风格"""
    name: str = "商品评测风格"
    description: str = "专业客观、实用导向的商品评测解说风格"
    channel_name: str = "翔宇评测"
    voice_id: str = "a1b2c3d4e5f6789012345678901234cd"
    language_voices: dict = {
        "中文": "a1b2c3d4e5f6789012345678901234cd",
        "英语": "e1f2g3h4i5j6789012345678901234ef"
    }
    
    def get_analysis_prompt(self, **kwargs) -> str:
        scene_count = kwargs.get('scene_count', 10)
        language = kwargs.get('language', '中文')
        
        return f"""# AI商品评测风格导演

你是一位专业的商品评测AI导演，擅长客观公正地分析产品特性。

## 评测原则
1. **客观公正**：基于事实进行评价，避免主观偏见
2. **实用导向**：关注用户真实需求和使用场景
3. **专业深度**：提供有价值的技术分析和建议

## 输入信息
- 分镜数量: {scene_count}
- 频道名称: {self.channel_name}
- 语言: {language}
- 时长范围: {self.min_scene_duration}-{self.max_scene_duration}秒

输出纯净JSON格式的分镜脚本，每个分镜都要体现专业评测的特色。"""
    
    def get_sync_prompt(self, **kwargs) -> str:
        scene_id = kwargs.get('scene_id')
        duration = kwargs.get('duration_seconds')
        language = kwargs.get('language', '中文')
        original_script = kwargs.get('original_script', '')
        
        return f"""# 商品评测风格旁白优化

为分镜{scene_id}（{duration}秒）优化旁白，保持专业评测风格。

原始脚本: {original_script}

要求：
1. 保持客观专业的语调
2. 突出产品特性和优缺点
3. 提供实用的购买建议

生成三个版本：
- v1: {int(duration * self.narration_speed_multipliers[0])} 字
- v2: {int(duration * self.narration_speed_multipliers[1])} 字
- v3: {int(duration * self.narration_speed_multipliers[2])} 字"""

# 注册风格
StyleRegistry.register(ProductReviewStyle())
