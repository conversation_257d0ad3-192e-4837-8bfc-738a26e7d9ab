"""配置管理模块"""

from .settings import settings
from .styles.base import StyleRegistry
from .styles import movie_styles, documentary_styles, commercial_styles

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.settings = settings
        self.style_registry = StyleRegistry
        self._load_all_styles()
    
    def _load_all_styles(self):
        """加载所有风格配置"""
        # 风格配置在导入时自动注册
        pass
    
    def get_style(self, style_name: str):
        """获取风格配置"""
        style = self.style_registry.get(style_name)
        if not style:
            raise ValueError(f"Unknown style: {style_name}")
        return style
    
    def list_available_styles(self) -> list:
        """列出可用风格"""
        return self.style_registry.list_styles()
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_keys = [
            'GEMINI_API_KEY', 
            'FISH_AUDIO_API_KEY'
        ]
        
        for key in required_keys:
            if not getattr(self.settings, key, None):
                raise ValueError(f"Missing required config: {key}")
        
        return True

# 全局配置管理器实例
config_manager = ConfigManager()
